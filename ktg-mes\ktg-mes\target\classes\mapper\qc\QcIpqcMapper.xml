<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcIpqcMapper">
    
    <resultMap type="QcIpqc" id="QcIpqcResult">
        <result property="ipqcId"    column="ipqc_id"    />
        <result property="ipqcCode"    column="ipqc_code"    />
        <result property="ipqcName"    column="ipqc_name"    />
        <result property="ipqcType"    column="ipqc_type"    />
        <result property="templateId"    column="template_id"    />
        <result property="sourceDocId" column="source_doc_id" ></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceLineId" column="source_line_id"></result>
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName" column="unit_name" />
        <result property="quantityCheck"    column="quantity_check"    />
        <result property="quantityUnqualified"    column="quantity_unqualified"    />
        <result property="quantityQualified"    column="quantity_qualified"    />
        <result property="quantityLaborScrap"    column="quantity_labor_scrap"    />
        <result property="quantityMaterialScrap"    column="quantity_material_scrap"    />
        <result property="quantityOtherScrap"    column="quantity_other_scrap"    />
        <result property="crRate"    column="cr_rate"    />
        <result property="majRate"    column="maj_rate"    />
        <result property="minRate"    column="min_rate"    />
        <result property="crQuantity"    column="cr_quantity"    />
        <result property="majQuantity"    column="maj_quantity"    />
        <result property="minQuantity"    column="min_quantity"    />
        <result property="checkResult"    column="check_result"    />
        <result property="inspectDate"    column="inspect_date"    />
        <result property="inspector"    column="inspector"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcIpqcVo">
        select ipqc_id, ipqc_code, ipqc_name, ipqc_type, template_id, source_doc_id,source_doc_type, source_doc_code, source_line_id, workorder_id, workorder_code, workorder_name, task_id, task_code, task_name, workstation_id, workstation_code, workstation_name, process_id, process_code, process_name, item_id, item_code, item_name, specification, unit_of_measure, unit_name, quantity_check, quantity_unqualified, quantity_qualified, quantity_labor_scrap, quantity_material_scrap, quantity_other_scrap, cr_rate, maj_rate, min_rate, cr_quantity, maj_quantity, min_quantity, check_result, inspect_date, inspector, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from qc_ipqc
    </sql>

    <select id="selectQcIpqcList" parameterType="QcIpqc" resultMap="QcIpqcResult">
        <include refid="selectQcIpqcVo"/>
        <where>  
            <if test="ipqcCode != null  and ipqcCode != ''"> and ipqc_code = #{ipqcCode}</if>
            <if test="ipqcName != null  and ipqcName != ''"> and ipqc_name like concat('%', #{ipqcName}, '%')</if>
            <if test="ipqcType != null  and ipqcType != ''"> and ipqc_type = #{ipqcType}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="sourceDocId != null"> and source_doc_id = #{sourceDocId}</if>
            <if test="sourceDocType !=null and sourceDocType !=''"> and source_doc_type = #{sourceDocType}</if>
            <if test="sourceDocCode !=null and sourceDocCode !=''"> and source_doc_code = #{sourceDocCode}</if>
            <if test="sourceLineId !=null"> and source_line_id = #{sourceLineId}</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
             <if test="unitName != null and unitName != ''">and unit_name = #{unitName}</if>
            <if test="quantityCheck != null "> and quantity_check = #{quantityCheck}</if>
            <if test="quantityUnqualified != null "> and quantity_unqualified = #{quantityUnqualified}</if>
            <if test="quantityQualified != null "> and quantity_qualified = #{quantityQualified}</if>
            <if test="quantityLaborScrap != null "> and quantity_labor_scrap = #{quantityLaborScrap}</if>
            <if test="quantityMaterialScrap != null "> and quantity_material_scrap = #{quantityMaterialScrap}</if>
            <if test="quantityOtherScrap != null "> and quantity_other_scrap = #{quantityOtherScrap}</if>
            <if test="crRate != null "> and cr_rate = #{crRate}</if>
            <if test="majRate != null "> and maj_rate = #{majRate}</if>
            <if test="minRate != null "> and min_rate = #{minRate}</if>
            <if test="crQuantity != null "> and cr_quantity = #{crQuantity}</if>
            <if test="majQuantity != null "> and maj_quantity = #{majQuantity}</if>
            <if test="minQuantity != null "> and min_quantity = #{minQuantity}</if>
            <if test="checkResult != null  and checkResult != ''"> and check_result = #{checkResult}</if>
            <if test="inspectDate != null "> and inspect_date = #{inspectDate}</if>
            <if test="inspector != null  and inspector != ''"> and inspector = #{inspector}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectQcIpqcByIpqcId" parameterType="Long" resultMap="QcIpqcResult">
        <include refid="selectQcIpqcVo"/>
        where ipqc_id = #{ipqcId}
    </select>

    <select id="checkIpqcCodeUnique" parameterType="QcIpqc" resultMap="QcIpqcResult">
        select * from qc_ipqc where ipqc_code = #{ipqcCode}
    </select>

    <select id="getProcessInfo" parameterType="QcIpqc" resultMap="QcIpqcResult">
        select pw.workorder_id, pw.workorder_code,pw.workorder_name, rp.process_id, rp.process_code,rp.process_name,mw.workstation_id, mw.workstation_code,mw.workstation_name
        from pro_workorder pw
                 left join pro_route_product prp
                           on pw.product_code = prp.item_code
                 left join pro_route_process rp
                           on rp.route_id = prp.route_id
                 left join md_workstation mw
                           on mw.process_id = rp.process_id
        <where>
            <if test="workorderId">and pw.workorder_id = #{workorderId}</if>
            <if test="workorderCode">and pw.workorder_code = #{workorderCode}</if>
            <if test="workstationId">and mw.workstation_id = #{workstationId}</if>
            <if test="workstationCode">and mw.workstation_code = #{workstationCode}</if>
        </where>
    </select>


    <insert id="insertQcIpqc" parameterType="QcIpqc" useGeneratedKeys="true" keyProperty="ipqcId">
        insert into qc_ipqc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ipqcCode != null and ipqcCode != ''">ipqc_code,</if>
            <if test="ipqcName != null">ipqc_name,</if>
            <if test="ipqcType != null and ipqcType != ''">ipqc_type,</if>
            <if test="templateId != null">template_id,</if>
            <if test="sourceDocId != null">source_doc_id,</if>
            <if test="sourceDocType !=null and sourceDocType !=''">source_doc_type,</if>
            <if test="sourceDocCode !=null and sourceDocCode !=''">source_doc_code,</if>
            <if test="sourceLineId !=null">source_line_id,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="workorderName != null and workorderName != ''">workorder_name,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null and workstationCode != ''">workstation_code,</if>
            <if test="workstationName != null and workstationName != ''">workstation_name,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="quantityCheck != null">quantity_check,</if>
            <if test="quantityUnqualified != null">quantity_unqualified,</if>
            <if test="quantityQualified != null">quantity_qualified,</if>
            <if test="quantityLaborScrap != null">quantity_labor_scrap,</if>
            <if test="quantityMaterialScrap != null">quantity_material_scrap,</if>
            <if test="quantityOtherScrap != null">quantity_other_scrap,</if>
            <if test="crRate != null">cr_rate,</if>
            <if test="majRate != null">maj_rate,</if>
            <if test="minRate != null">min_rate,</if>
            <if test="crQuantity != null">cr_quantity,</if>
            <if test="majQuantity != null">maj_quantity,</if>
            <if test="minQuantity != null">min_quantity,</if>
            <if test="checkResult != null">check_result,</if>
            <if test="inspectDate != null">inspect_date,</if>
            <if test="inspector != null">inspector,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ipqcCode != null and ipqcCode != ''">#{ipqcCode},</if>
            <if test="ipqcName != null">#{ipqcName},</if>
            <if test="ipqcType != null and ipqcType != ''">#{ipqcType},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="sourceDocId != null">#{sourceDocId},</if>
            <if test="sourceDocType !=null and sourceDocType !=''">#{sourceDocType},</if>
            <if test="sourceDocCode !=null and sourceDocCode !=''">#{sourceDocCode},</if>
            <if test="sourceLineId !=null">#{sourceLineId},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">#{workorderName},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null and workstationCode != ''">#{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">#{workstationName},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="quantityCheck != null">#{quantityCheck},</if>
            <if test="quantityUnqualified != null">#{quantityUnqualified},</if>
            <if test="quantityQualified != null">#{quantityQualified},</if>
            <if test="quantityLaborScrap != null">#{quantityLaborScrap},</if>
            <if test="quantityMaterialScrap != null">#{quantityMaterialScrap},</if>
            <if test="quantityOtherScrap != null">#{quantityOtherScrap},</if>
            <if test="crRate != null">#{crRate},</if>
            <if test="majRate != null">#{majRate},</if>
            <if test="minRate != null">#{minRate},</if>
            <if test="crQuantity != null">#{crQuantity},</if>
            <if test="majQuantity != null">#{majQuantity},</if>
            <if test="minQuantity != null">#{minQuantity},</if>
            <if test="checkResult != null">#{checkResult},</if>
            <if test="inspectDate != null">#{inspectDate},</if>
            <if test="inspector != null">#{inspector},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQcIpqc" parameterType="QcIpqc">
        update qc_ipqc
        <trim prefix="SET" suffixOverrides=",">
            <if test="ipqcCode != null and ipqcCode != ''">ipqc_code = #{ipqcCode},</if>
            <if test="ipqcName != null">ipqc_name = #{ipqcName},</if>
            <if test="ipqcType != null and ipqcType != ''">ipqc_type = #{ipqcType},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="sourceDocId !=null">source_doc_id = #{sourceDocId},</if>
            <if test="sourceDocType !=null and sourceDocType !=''">source_doc_type = #{sourceDocType},</if>
            <if test="sourceDocCode !=null and sourceDocCode !=''">source_doc_code = #{sourceDocCode},</if>
            <if test="sourceLineId !=null">source_line_id = #{sourceLineId},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">workorder_name = #{workorderName},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null and workstationCode != ''">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">workstation_name = #{workstationName},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="quantityCheck != null">quantity_check = #{quantityCheck},</if>
            <if test="quantityUnqualified != null">quantity_unqualified = #{quantityUnqualified},</if>
            <if test="quantityQualified != null">quantity_qualified = #{quantityQualified},</if>
            <if test="quantityLaborScrap != null">quantity_labor_scrap = #{quantityLaborScrap},</if>
            <if test="quantityMaterialScrap != null">quantity_material_scrap = #{quantityMaterialScrap},</if>
            <if test="quantityOtherScrap != null">quantity_other_scrap = #{quantityOtherScrap},</if>
            <if test="crRate != null">cr_rate = #{crRate},</if>
            <if test="majRate != null">maj_rate = #{majRate},</if>
            <if test="minRate != null">min_rate = #{minRate},</if>
            <if test="crQuantity != null">cr_quantity = #{crQuantity},</if>
            <if test="majQuantity != null">maj_quantity = #{majQuantity},</if>
            <if test="minQuantity != null">min_quantity = #{minQuantity},</if>
            <if test="checkResult != null">check_result = #{checkResult},</if>
            <if test="inspectDate != null">inspect_date = #{inspectDate},</if>
            <if test="inspector != null">inspector = #{inspector},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ipqc_id = #{ipqcId}
    </update>

    <update id="updateCrMajMinQuaAndRate" parameterType="Long">
        UPDATE qc_ipqc a
            INNER JOIN (
            SELECT SUM(CASE WHEN defect_level = 'CR' THEN defect_quantity ELSE 0 END ) AS cr_quantity,
            SUM(CASE WHEN defect_level = 'MAJ' THEN defect_quantity ELSE 0 END) AS maj_quantity,
            SUM(CASE WHEN defect_level = 'MIN' THEN defect_quantity ELSE 0 END) AS min_quantity,
            qid.`qc_id` as 'ipqc_id'
            FROM qc_defect_record qid
            WHERE qid.qc_id = #{ipqcId}
            and qid.qc_type = 'IPQC'
            GROUP BY qid.qc_id
            ) b
        ON a.`ipqc_id` = b.ipqc_id
            SET a.cr_quantity=b.cr_quantity,a.maj_quantity=b.maj_quantity,a.min_quantity=b.min_quantity,
                a.`cr_rate`= ROUND(b.cr_quantity/a.`quantity_check`*100,2),
                a.`maj_rate`= ROUND(b.maj_quantity/a.`quantity_check`*100,2),
                a.`min_rate`= ROUND(b.min_quantity/a.`quantity_check`*100,2)
        WHERE a.ipqc_id = #{ipqcId}
    </update>

    <delete id="deleteQcIpqcByIpqcId" parameterType="Long">
        delete from qc_ipqc where ipqc_id = #{ipqcId}
    </delete>

    <delete id="deleteQcIpqcByIpqcIds" parameterType="String">
        delete from qc_ipqc where ipqc_id in 
        <foreach item="ipqcId" collection="array" open="(" separator="," close=")">
            #{ipqcId}
        </foreach>
    </delete>
</mapper>