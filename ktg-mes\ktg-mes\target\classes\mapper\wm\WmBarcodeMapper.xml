<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmBarcodeMapper">
    
    <resultMap type="WmBarcode" id="WmBarcodeResult">
        <result property="barcodeId"    column="barcode_id"    />
        <result property="barcodeFormart"    column="barcode_formart"    />
        <result property="barcodeType"    column="barcode_type"    />
        <result property="barcodeContent"    column="barcode_content"    />
        <result property="bussinessId" column="bussiness_id"></result>
        <result property="bussinessCode" column="bussiness_code"></result>
        <result property="bussinessName" column="bussiness_name"></result>
        <result property="barcodeUrl"    column="barcode_url"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmBarcodeVo">
        select barcode_id, barcode_formart, barcode_type, barcode_content,bussiness_id,bussiness_code,bussiness_name, barcode_url, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_barcode
    </sql>

    <select id="selectWmBarcodeList" parameterType="WmBarcode" resultMap="WmBarcodeResult">
        <include refid="selectWmBarcodeVo"/>
        <where>  
            <if test="barcodeFormart != null  and barcodeFormart != ''"> and barcode_formart = #{barcodeFormart}</if>
            <if test="barcodeType != null  and barcodeType != ''"> and barcode_type = #{barcodeType}</if>
            <if test="barcodeContent != null  and barcodeContent != ''"> and barcode_content = #{barcodeContent}</if>
            <if test="bussinessId != null"> and bussiness_id = #{bussinessId}</if>
            <if test="bussinessCode != null and bussinessCode !=''"> and bussiness_code = #{bussinessCode}</if>
            <if test="bussinessName != null and bussinessName !=''"> and bussiness_name = #{bussinessName}</if>
            <if test="barcodeUrl != null  and barcodeUrl != ''"> and barcode_url = #{barcodeUrl}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmBarcodeByBarcodeId" parameterType="Long" resultMap="WmBarcodeResult">
        <include refid="selectWmBarcodeVo"/>
        where barcode_id = #{barcodeId}
    </select>

    <select id="checkBarcodeUnique" parameterType="WmBarcode" resultMap="WmBarcodeResult">
        <include refid="selectWmBarcodeVo"/>
        where bussiness_id = #{bussinessId} and barcode_type = #{barcodeType} and bussiness_code = #{bussinessCode}
    </select>

    <insert id="insertWmBarcode" parameterType="WmBarcode" useGeneratedKeys="true" keyProperty="barcodeId">
        insert into wm_barcode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="barcodeFormart != null and barcodeFormart != ''">barcode_formart,</if>
            <if test="barcodeType != null and barcodeType != ''">barcode_type,</if>
            <if test="barcodeContent != null and barcodeContent != ''">barcode_content,</if>
            <if test="bussinessId != null"> bussiness_id,</if>
            <if test="bussinessCode != null and bussinessCode !=''">bussiness_code,</if>
            <if test="bussinessName != null and bussinessName !=''">bussiness_name,</if>
            <if test="barcodeUrl != null  and barcodeUrl != ''">barcode_url,</if>
            <if test="enableFlag != null">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="barcodeFormart != null and barcodeFormart != ''">#{barcodeFormart},</if>
            <if test="barcodeType != null and barcodeType != ''">#{barcodeType},</if>
            <if test="barcodeContent != null and barcodeContent != ''">#{barcodeContent},</if>
            <if test="bussinessId != null"> #{bussinessId},</if>
            <if test="bussinessCode != null and bussinessCode !=''">#{bussinessCode},</if>
            <if test="bussinessName != null and bussinessName !=''">#{bussinessName},</if>
            <if test="barcodeUrl != null  and barcodeUrl != ''">#{barcodeUrl},</if>
            <if test="enableFlag != null">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmBarcode" parameterType="WmBarcode">
        update wm_barcode
        <trim prefix="SET" suffixOverrides=",">
            <if test="barcodeFormart != null and barcodeFormart != ''">barcode_formart = #{barcodeFormart},</if>
            <if test="barcodeType != null and barcodeType != ''">barcode_type = #{barcodeType},</if>
            <if test="barcodeContent != null and barcodeContent != ''">barcode_content = #{barcodeContent},</if>
            <if test="bussinessId != null">bussiness_id = #{bussinessId},</if>
            <if test="bussinessCode != null and bussinessCode !=''">bussiness_code = #{bussinessCode},</if>
            <if test="bussinessName != null and bussinessName !=''">bussiness_name = #{bussinessName},</if>
            <if test="barcodeUrl != null  and barcodeUrl != ''">barcode_url = #{barcodeUrl},</if>
            <if test="enableFlag != null">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where barcode_id = #{barcodeId}
    </update>

    <delete id="deleteWmBarcodeByBarcodeId" parameterType="Long">
        delete from wm_barcode where barcode_id = #{barcodeId}
    </delete>

    <delete id="deleteWmBarcodeByBarcodeIds" parameterType="String">
        delete from wm_barcode where barcode_id in 
        <foreach item="barcodeId" collection="array" open="(" separator="," close=")">
            #{barcodeId}
        </foreach>
    </delete>
</mapper>