<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProCardProcessMapper">
    
    <resultMap type="ProCardProcess" id="ProCardProcessResult">
        <result property="recordId"    column="record_id"    />
        <result property="cardId"    column="card_id"    />
        <result property="cardCode"    column="card_code"    />
        <result property="seqNum"    column="seq_num"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="inputTime"    column="input_time"    />
        <result property="outputTime"    column="output_time"    />
        <result property="quantityInput"    column="quantity_input"    />
        <result property="quantityOutput"    column="quantity_output"    />
        <result property="quantityUnquanlify"    column="quantity_unquanlify"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="ipqcId"    column="ipqc_id"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProCardProcessVo">
        select record_id, card_id, card_code, seq_num, process_id, process_code, process_name, input_time, output_time, quantity_input, quantity_output, quantity_unquanlify, workstation_id, workstation_code, workstation_name, user_id, user_name, nick_name, ipqc_id, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_card_process
    </sql>

    <select id="selectProCardProcessList" parameterType="ProCardProcess" resultMap="ProCardProcessResult">
        <include refid="selectProCardProcessVo"/>
        <where>  
            <if test="cardId != null "> and card_id = #{cardId}</if>
            <if test="cardCode != null  and cardCode != ''"> and card_code = #{cardCode}</if>
            <if test="seqNum != null "> and seq_num = #{seqNum}</if>
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="inputTime != null "> and input_time = #{inputTime}</if>
            <if test="outputTime != null "> and output_time = #{outputTime}</if>
            <if test="quantityInput != null "> and quantity_input = #{quantityInput}</if>
            <if test="quantityOutput != null "> and quantity_output = #{quantityOutput}</if>
            <if test="quantityUnquanlify != null "> and quantity_unquanlify = #{quantityUnquanlify}</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="ipqcId != null "> and ipqc_id = #{ipqcId}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>
    
    <select id="selectProCardProcessByRecordId" parameterType="Long" resultMap="ProCardProcessResult">
        <include refid="selectProCardProcessVo"/>
        where record_id = #{recordId}
    </select>
        
    <insert id="insertProCardProcess" parameterType="ProCardProcess" useGeneratedKeys="true" keyProperty="recordId">
        insert into pro_card_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardId != null">card_id,</if>
            <if test="cardCode != null">card_code,</if>
            <if test="seqNum != null">seq_num,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="inputTime != null">input_time,</if>
            <if test="outputTime != null">output_time,</if>
            <if test="quantityInput != null">quantity_input,</if>
            <if test="quantityOutput != null">quantity_output,</if>
            <if test="quantityUnquanlify != null">quantity_unquanlify,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="ipqcId != null">ipqc_id,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cardId != null">#{cardId},</if>
            <if test="cardCode != null">#{cardCode},</if>
            <if test="seqNum != null">#{seqNum},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="inputTime != null">#{inputTime},</if>
            <if test="outputTime != null">#{outputTime},</if>
            <if test="quantityInput != null">#{quantityInput},</if>
            <if test="quantityOutput != null">#{quantityOutput},</if>
            <if test="quantityUnquanlify != null">#{quantityUnquanlify},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="ipqcId != null">#{ipqcId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProCardProcess" parameterType="ProCardProcess">
        update pro_card_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="cardId != null">card_id = #{cardId},</if>
            <if test="cardCode != null">card_code = #{cardCode},</if>
            <if test="seqNum != null">seq_num = #{seqNum},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="inputTime != null">input_time = #{inputTime},</if>
            <if test="outputTime != null">output_time = #{outputTime},</if>
            <if test="quantityInput != null">quantity_input = #{quantityInput},</if>
            <if test="quantityOutput != null">quantity_output = #{quantityOutput},</if>
            <if test="quantityUnquanlify != null">quantity_unquanlify = #{quantityUnquanlify},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="ipqcId != null">ipqc_id = #{ipqcId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteProCardProcessByRecordId" parameterType="Long">
        delete from pro_card_process where record_id = #{recordId}
    </delete>

    <delete id="deleteProCardProcessByRecordIds" parameterType="String">
        delete from pro_card_process where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>