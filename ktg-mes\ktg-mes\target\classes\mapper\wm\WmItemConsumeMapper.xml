<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmItemConsumeMapper">
    
    <resultMap type="WmItemConsume" id="WmItemConsumeResult">
        <result property="recordId"    column="record_id"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="feedbackId"    column="feedback_id"    />
        <result property="consumeDate"    column="consume_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="ItemConsumeTxBeanResult" type="ItemConsumeTxBean">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="unitName" column="unit_name"></result>
        <result property="batchId" column="batch_id"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <sql id="selectWmItemConsumeVo">
        select record_id, workorder_id, workorder_code, workorder_name, task_id, task_code, task_name, workstation_id, workstation_code, workstation_name, process_id, process_code, process_name, feedback_id, consume_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_item_consume
    </sql>

    <select id="selectWmItemConsumeList" parameterType="WmItemConsume" resultMap="WmItemConsumeResult">
        <include refid="selectWmItemConsumeVo"/>
        <where>  
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="feedbackId != null "> and feedback_id = #{feedbackId}</if>
            <if test="consumeDate != null "> and consume_date = #{consumeDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWmItemConsumeByRecordId" parameterType="Long" resultMap="WmItemConsumeResult">
        <include refid="selectWmItemConsumeVo"/>
        where record_id = #{recordId}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="ItemConsumeTxBeanResult">
        SELECT  irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`, irl.`unit_name`,ird.`batch_id`, ird.`batch_code`,
                ird.`warehouse_id`,ird.`warehouse_code`,ird.`warehouse_name`,ird.`location_id`,ird.`location_code`,ird.`location_name`,ird.`area_id`,ird.`area_code`,ird.`area_name`,
                'ITEM_CONSUME' AS source_doc_type,ir.`record_id` AS source_doc_id,"" AS source_doc_code,irl.`line_id` AS source_doc_line_id,
                ird.`quantity` AS transaction_quantity,
                ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`
        FROM wm_item_consume ir
                 LEFT JOIN wm_item_consume_line irl
                           ON ir.record_id = irl.`record_id`
                 LEFT JOIN wm_item_consume_detail ird
                           ON irl.line_id = ird.`line_id` and ir.record_id = ird.`record_id`
        WHERE ir.`record_id` = #{recordId}
    </select>

    <insert id="insertWmItemConsume" parameterType="WmItemConsume" useGeneratedKeys="true" keyProperty="recordId">
        insert into wm_item_consume
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="workorderName != null">workorder_name,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null">task_code,</if>
            <if test="taskName != null">task_name,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="feedbackId != null">feedback_id,</if>
            <if test="consumeDate != null">consume_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="workorderName != null">#{workorderName},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="feedbackId != null">#{feedbackId},</if>
            <if test="consumeDate != null">#{consumeDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmItemConsume" parameterType="WmItemConsume">
        update wm_item_consume
        <trim prefix="SET" suffixOverrides=",">
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null">workorder_name = #{workorderName},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null">task_code = #{taskCode},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="feedbackId != null">feedback_id = #{feedbackId},</if>
            <if test="consumeDate != null">consume_date = #{consumeDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteWmItemConsumeByRecordId" parameterType="Long">
        delete from wm_item_consume where record_id = #{recordId}
    </delete>

    <delete id="deleteWmItemConsumeByRecordIds" parameterType="String">
        delete from wm_item_consume where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>