<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProFeedbackMapper">
    
    <resultMap type="ProFeedback" id="ProFeedbackResult">
        <result property="recordId"    column="record_id"    />
        <result property="feedbackCode" column="feedback_code" />
        <result property="feedbackType"    column="feedback_type"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="routeId"      column="route_id"    />
        <result property="routeCode"    column="route_code"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="isCheck"    column="is_check"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure" column="unit_of_measure" ></result>
        <result property="unitName"    column="unit_name"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="lotNumber"    column="lot_number"    />
        <result property="quantity"    column="quantity"    />
        <result property="quantityFeedback"    column="quantity_feedback"    />
        <result property="quantityQualified"    column="quantity_qualified"    />
        <result property="quantityUnquanlified"    column="quantity_unquanlified"    />
        <result property="quantityUncheck"    column="quantity_uncheck"    />
        <result property="quantityLaborScrap"    column="quantity_labor_scrap"    />
        <result property="quantityMaterialScrap"    column="quantity_material_scrap"    />
        <result property="quantityOtherScrap"    column="quantity_other_scrap"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="feedbackChannel"    column="feedback_channel"    />
        <result property="feedbackTime"    column="feedback_time"    />
        <result property="recordUser"    column="record_user"    />
        <result property="recordNick"    column="record_nick" />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProFeedbackVo">
        select f.record_id, f.feedback_code, f.feedback_type, f.workstation_id, f.workstation_code, f.workstation_name,f.route_id,f.route_code, f.process_id, f.process_code, f.process_name,p.is_check, f.workorder_id, f.workorder_code, f.workorder_name, f.task_id, f.task_code,f.item_id,f.item_code,f.item_name,f.specification,f.unit_of_measure, f.unit_name, f.expire_date, f.lot_number, f.quantity, f.quantity_feedback, f.quantity_qualified, f.quantity_unquanlified,f.quantity_uncheck, f.quantity_labor_scrap, f.quantity_material_scrap, f.quantity_other_scrap, f.user_name, f.nick_name, f.feedback_channel, f.feedback_time,f.record_user,f.record_nick,f.status, f.remark, f.attr1, f.attr2, f.attr3, f.attr4, f.create_by, f.create_time, f.update_by, f.update_time
        from pro_feedback f
            left join pro_route_process p
                on f.process_id = p.process_id and f.route_id = p.route_id
    </sql>

    <select id="selectProFeedbackList" parameterType="ProFeedback" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        <where>  
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="feedbackCode !=null "> and feedback_code = #{feedbackCode}</if>
            <if test="feedbackType !=null "> and feedback_type = #{feedbackType}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="processId != null "> and f.process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and f.process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and f.process_name like concat('%', #{processName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="expireDate != null "> and expire_date = #{expireDate}</if>
            <if test="lotNumber != null  and lotNumber != ''"> and lot_number = #{lotNumber}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="quantityFeedback != null "> and quantity_feedback = #{quantityFeedback}</if>
            <if test="quantityQualified != null "> and quantity_qualified = #{quantityQualified}</if>
            <if test="quantityUnquanlified != null "> and quantity_unquanlified = #{quantityUnquanlified}</if>
            <if test="quantityUncheck != null "> and quantity_uncheck = #{quantityUncheck}</if>
            <if test="quantityLaborScrap != null "> and quantity_labor_scrap = #{quantityLaborScrap}</if>
            <if test="quantityMaterialScrap != null "> and quantity_material_scrap = #{quantityMaterialScrap}</if>
            <if test="quantityOtherScrap != null "> and quantity_other_scrap = #{quantityOtherScrap}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="feedbackChannel != null  and feedbackChannel != ''"> and feedback_channel = #{feedbackChannel}</if>
            <if test="feedbackTime != null "> and feedback_time = #{feedbackTime}</if>
            <if test="recordUser != null "> and record_user = #{recordUser}</if>
            <if test="recordNick != null "> and record_nick = #{recordNick}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProFeedbackByRecordId" parameterType="Long" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        where f.record_id = #{recordId}
    </select>
    <select id="selectByWorkorderIds" resultType="com.ktg.mes.pro.domain.ProFeedback" resultMap="ProFeedbackResult">
        <include refid="selectProFeedbackVo"/>
        where f.workorder_id in
            <foreach collection="workorderIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

    <insert id="insertProFeedback" parameterType="ProFeedback" useGeneratedKeys="true" keyProperty="recordId">
        insert into pro_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="feedbackType !=null ">feedback_type,</if>
            <if test="feedbackCode !=null ">feedback_code,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="routeId !=null">route_id,</if>
            <if test="routeCode !=null">route_code,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="workorderName != null">workorder_name,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null">task_code,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="lotNumber != null and lotNumber != ''">lot_number,</if>
            <if test="quantity != null">quantity,</if>
            <if test="quantityFeedback != null">quantity_feedback,</if>
            <if test="quantityQualified != null">quantity_qualified,</if>
            <if test="quantityUnquanlified != null">quantity_unquanlified,</if>
            <if test="quantityUncheck != null ">quantity_uncheck,</if>
            <if test="quantityLaborScrap != null ">quantity_labor_scrap,</if>
            <if test="quantityMaterialScrap != null ">quantity_material_scrap,</if>
            <if test="quantityOtherScrap != null ">quantity_other_scrap,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="feedbackChannel != null">feedback_channel,</if>
            <if test="feedbackTime != null">feedback_time,</if>
            <if test="recordUser != null">record_user,</if>
            <if test="recordNick != null ">record_nick,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="feedbackType !=null ">#{feedbackType},</if>
            <if test="feedbackCode !=null ">#{feedbackCode},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="routeId !=null">#{routeId},</if>
            <if test="routeCode !=null">#{routeCode},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="workorderName != null">#{workorderName},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="lotNumber != null and lotNumber != ''">#{lotNumber},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="quantityFeedback != null">#{quantityFeedback},</if>
            <if test="quantityQualified != null">#{quantityQualified},</if>
            <if test="quantityUnquanlified != null">#{quantityUnquanlified},</if>
            <if test="quantityUncheck != null ">#{quantityUncheck},</if>
            <if test="quantityLaborScrap != null ">#{quantityLaborScrap},</if>
            <if test="quantityMaterialScrap != null ">#{quantityMaterialScrap},</if>
            <if test="quantityOtherScrap != null ">#{quantityOtherScrap},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="feedbackChannel != null">#{feedbackChannel},</if>
            <if test="feedbackTime != null">#{feedbackTime},</if>
            <if test="recordUser != null">#{recordUser},</if>
            <if test="recordNick != null ">#{recordNick},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProFeedback" parameterType="ProFeedback">
        update pro_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="feedbackType !=null ">feedback_type = #{feedbackType},</if>
            <if test="feedbackCode !=null ">feedback_code = #{feedbackCode},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="routeId !=null">route_id = #{routeId},</if>
            <if test="routeCode !=null">route_code = #{routeCode},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null">workorder_name = #{workorderName},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null">task_code = #{taskCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="lotNumber != null and lotNumber != ''">lot_number = #{lotNumber},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="quantityFeedback != null">quantity_feedback = #{quantityFeedback},</if>
            <if test="quantityQualified != null">quantity_qualified = #{quantityQualified},</if>
            <if test="quantityUnquanlified != null">quantity_unquanlified = #{quantityUnquanlified},</if>
            <if test="quantityUncheck != null ">quantity_uncheck = #{quantityUncheck},</if>
            <if test="quantityLaborScrap != null ">quantity_labor_scrap = #{quantityLaborScrap},</if>
            <if test="quantityMaterialScrap != null ">quantity_material_scrap = #{quantityMaterialScrap},</if>
            <if test="quantityOtherScrap != null ">quantity_other_scrap = #{quantityOtherScrap},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="feedbackChannel != null">feedback_channel = #{feedbackChannel},</if>
            <if test="feedbackTime != null">feedback_time = #{feedbackTime},</if>
            <if test="recordUser != null">record_user = #{recordUser},</if>
            <if test="recordNick != null ">record_nick = #{recordNick},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteProFeedbackByRecordId" parameterType="Long">
        delete from pro_feedback where record_id = #{recordId}
    </delete>

    <delete id="deleteProFeedbackByRecordIds" parameterType="String">
        delete from pro_feedback where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>