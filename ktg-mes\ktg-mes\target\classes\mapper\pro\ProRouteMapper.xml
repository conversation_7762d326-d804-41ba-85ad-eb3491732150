<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProRouteMapper">
    
    <resultMap type="ProRoute" id="ProRouteResult">
        <result property="routeId"    column="route_id"    />
        <result property="routeCode"    column="route_code"    />
        <result property="routeName"    column="route_name"    />
        <result property="routeDesc"    column="route_desc"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProRouteVo">
        select route_id, route_code, route_name, route_desc, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_route
    </sql>

    <select id="selectProRouteList" parameterType="ProRoute" resultMap="ProRouteResult">
        <include refid="selectProRouteVo"/>
        <where>  
            <if test="routeCode != null  and routeCode != ''"> and route_code = #{routeCode}</if>
            <if test="routeName != null  and routeName != ''"> and route_name like concat('%', #{routeName}, '%')</if>
            <if test="routeDesc != null  and routeDesc != ''"> and route_desc = #{routeDesc}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProRouteByRouteId" parameterType="Long" resultMap="ProRouteResult">
        <include refid="selectProRouteVo"/>
        where route_id = #{routeId}
    </select>

    <select id="getRouteByProductId" parameterType="Long" resultMap="ProRouteResult">
        select r.*
        from pro_route r
                 left join pro_route_product p
                           on p.route_id = r.route_id
        where r.enable_flag = 'Y'
          and p.item_id = #{itemId}
    </select>

    <select id="checkRouteCodeUnique" parameterType="ProRoute" resultMap="ProRouteResult">
        <include refid="selectProRouteVo"/>
        where route_code = #{routeCode} limit 1
    </select>

    <insert id="insertProRoute" parameterType="ProRoute" useGeneratedKeys="true" keyProperty="routeId">
        insert into pro_route
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="routeCode != null and routeCode != ''">route_code,</if>
            <if test="routeName != null and routeName != ''">route_name,</if>
            <if test="routeDesc != null">route_desc,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="routeCode != null and routeCode != ''">#{routeCode},</if>
            <if test="routeName != null and routeName != ''">#{routeName},</if>
            <if test="routeDesc != null">#{routeDesc},</if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProRoute" parameterType="ProRoute">
        update pro_route
        <trim prefix="SET" suffixOverrides=",">
            <if test="routeCode != null and routeCode != ''">route_code = #{routeCode},</if>
            <if test="routeName != null and routeName != ''">route_name = #{routeName},</if>
            <if test="routeDesc != null">route_desc = #{routeDesc},</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where route_id = #{routeId}
    </update>

    <delete id="deleteProRouteByRouteId" parameterType="Long">
        delete from pro_route where route_id = #{routeId}
    </delete>

    <delete id="deleteProRouteByRouteIds" parameterType="String">
        delete from pro_route where route_id in 
        <foreach item="routeId" collection="array" open="(" separator="," close=")">
            #{routeId}
        </foreach>
    </delete>
</mapper>