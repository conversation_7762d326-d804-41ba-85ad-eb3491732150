<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmMiscRecptMapper">
    
    <resultMap type="WmMiscRecpt" id="WmMiscRecptResult">
        <result property="recptId"    column="recpt_id"    />
        <result property="recptCode"    column="recpt_code"    />
        <result property="recptName"    column="recpt_name"    />
        <result property="miscType"    column="misc_type"    />
        <result property="sourceDocId"    column="source_doc_id"    />
        <result property="sourceDocCode"    column="source_doc_code"    />
        <result property="sourceDocType"    column="source_doc_type"    />
        <result property="recptDate"    column="recpt_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="MiscRecptTxBean"  id="MiscRecptTxBeanResult">
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="unitName" column="unit_name"></result>
        <result property="batchId" column="batch_id"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="recptDate" column="recpt_date"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmMiscRecptVo">
        select recpt_id, recpt_code, recpt_name, misc_type, source_doc_id, source_doc_code, source_doc_type, recpt_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_misc_recpt
    </sql>

    <select id="selectWmMiscRecptList" parameterType="WmMiscRecpt" resultMap="WmMiscRecptResult">
        <include refid="selectWmMiscRecptVo"/>
        <where>  
            <if test="recptCode != null  and recptCode != ''"> and recpt_code = #{recptCode}</if>
            <if test="recptName != null  and recptName != ''"> and recpt_name like concat('%', #{recptName}, '%')</if>
            <if test="miscType != null  and miscType != ''"> and misc_type = #{miscType}</if>
            <if test="sourceDocId != null "> and source_doc_id = #{sourceDocId}</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and source_doc_code = #{sourceDocCode}</if>
            <if test="sourceDocType != null  and sourceDocType != ''"> and source_doc_type = #{sourceDocType}</if>
            <if test="recptDate != null "> and recpt_date = #{recptDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWmMiscRecptByRecptId" parameterType="Long" resultMap="WmMiscRecptResult">
        <include refid="selectWmMiscRecptVo"/>
        where recpt_id = #{recptId}
    </select>

    <select id="checkRecptCodeUnique" parameterType="WmMiscRecpt" resultMap="WmMiscRecptResult">
        <include refid="selectWmMiscRecptVo"/>
        where recpt_code = #{recptCode}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="MiscRecptTxBeanResult">
        SELECT
            irl.`item_id`,
            irl.`item_code`,
            irl.`item_name`,
            irl.`specification`,
            irl.`unit_of_measure`,
            irl.`unit_name`,
            irl.`batch_id`,
            irl.`batch_code`,
            ird.`warehouse_id`,
            ird.`warehouse_code`,
            ird.`warehouse_name`,
            ird.`location_id`,
            ird.`location_code`,
            ird.`location_name`,
            ird.`area_id`,
            ird.`area_code`,
            ird.`area_name`,
            'MISC_RECPT' AS source_doc_type,
            ir.`recpt_id` AS source_doc_id,
            ir.`recpt_code` AS source_doc_code,
            irl.`line_id` AS source_doc_line_id,
            ird.`quantity` AS transaction_quantity,
            ir.recpt_date,
            ir.`create_by`,
            ir.`create_time`,
            ir.`update_by`,
            ir.`update_time`
        FROM
            wm_misc_recpt ir
                LEFT JOIN wm_misc_recpt_line irl ON ir.recpt_id = irl.`recpt_id`
                LEFT JOIN wm_misc_recpt_detail ird on irl.line_id = ird.line_id and irl.recpt_id = ird.recpt_id
        WHERE
            ir.`recpt_id` = #{recptId}
    </select>


    <insert id="insertWmMiscRecpt" parameterType="WmMiscRecpt" useGeneratedKeys="true" keyProperty="recptId">
        insert into wm_misc_recpt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">recpt_code,</if>
            <if test="recptName != null and recptName != ''">recpt_name,</if>
            <if test="miscType != null">misc_type,</if>
            <if test="sourceDocId != null">source_doc_id,</if>
            <if test="sourceDocCode != null">source_doc_code,</if>
            <if test="sourceDocType != null">source_doc_type,</if>
            <if test="recptDate != null">recpt_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">#{recptCode},</if>
            <if test="recptName != null and recptName != ''">#{recptName},</if>
            <if test="miscType != null">#{miscType},</if>
            <if test="sourceDocId != null">#{sourceDocId},</if>
            <if test="sourceDocCode != null">#{sourceDocCode},</if>
            <if test="sourceDocType != null">#{sourceDocType},</if>
            <if test="recptDate != null">#{recptDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmMiscRecpt" parameterType="WmMiscRecpt">
        update wm_misc_recpt
        <trim prefix="SET" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">recpt_code = #{recptCode},</if>
            <if test="recptName != null and recptName != ''">recpt_name = #{recptName},</if>
            <if test="miscType != null">misc_type = #{miscType},</if>
            <if test="sourceDocId != null">source_doc_id = #{sourceDocId},</if>
            <if test="sourceDocCode != null">source_doc_code = #{sourceDocCode},</if>
            <if test="sourceDocType != null">source_doc_type = #{sourceDocType},</if>
            <if test="recptDate != null">recpt_date = #{recptDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where recpt_id = #{recptId}
    </update>

    <delete id="deleteWmMiscRecptByRecptId" parameterType="Long">
        delete from wm_misc_recpt where recpt_id = #{recptId}
    </delete>

    <delete id="deleteWmMiscRecptByRecptIds" parameterType="String">
        delete from wm_misc_recpt where recpt_id in 
        <foreach item="recptId" collection="array" open="(" separator="," close=")">
            #{recptId}
        </foreach>
    </delete>
</mapper>