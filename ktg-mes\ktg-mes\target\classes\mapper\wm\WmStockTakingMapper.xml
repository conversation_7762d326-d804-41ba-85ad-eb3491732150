<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmStockTakingMapper">
    
    <resultMap type="WmStockTaking" id="WmStockTakingResult">
        <result property="takingId"    column="taking_id"    />
        <result property="takingCode"    column="taking_code"    />
        <result property="takingName"    column="taking_name"    />
        <result property="takingDate"    column="taking_date"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="takingType"    column="taking_type"    />
        <result property="blindFlag"    column="blind_flag"    />
        <result property="frozenFlag"    column="frozen_flag"    />
        <result property="planId"    column="plan_id"    />
        <result property="planCode"    column="plan_code"    />
        <result property="planName"    column="plan_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmStockTakingVo">
        select taking_id, taking_code, taking_name, taking_date, user_name, nick_name, taking_type, blind_flag, frozen_flag, plan_id, plan_code, plan_name, start_time, end_time, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_stock_taking
    </sql>

    <select id="selectWmStockTakingList" parameterType="WmStockTaking" resultMap="WmStockTakingResult">
        <include refid="selectWmStockTakingVo"/>
        <where>  
            <if test="takingCode != null  and takingCode != ''"> and taking_code = #{takingCode}</if>
            <if test="takingName != null  and takingName != ''"> and taking_name like concat('%', #{takingName}, '%')</if>
            <if test="takingDate != null "> and taking_date = #{takingDate}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="takingType != null  and takingType != ''"> and taking_type = #{takingType}</if>
             <if test="blindFlag != null  and blindFlag != ''"> and blind_flag = #{blindFlag}</if>
            <if test="frozenFlag != null  and frozenFlag != ''"> and frozen_flag = #{frozenFlag}</if>
            <if test="planId != null  and planId != ''"> and plan_id = #{planId}</if>
            <if test="planCode != null  and planCode != ''"> and plan_code like concat('%', #{planCode}, '%')</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmStockTakingByTakingId" parameterType="Long" resultMap="WmStockTakingResult">
        <include refid="selectWmStockTakingVo"/>
        where taking_id = #{takingId}
    </select>

    <select id="checkUnique" parameterType="WmStockTaking" resultMap="WmStockTakingResult">
        <include refid="selectWmStockTakingVo"/>
        where taking_code = #{takingCode}
    </select>
        
    <insert id="insertWmStockTaking" parameterType="WmStockTaking" useGeneratedKeys="true" keyProperty="takingId">
        insert into wm_stock_taking
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="takingCode != null and takingCode != ''">taking_code,</if>
            <if test="takingName != null">taking_name,</if>
            <if test="takingDate != null">taking_date,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="takingType != null and takingType != ''">taking_type,</if>
            <if test="blindFlag != null and blindFlag != ''">blind_flag,</if>
            <if test="frozenFlag != null and frozenFlag != ''">frozen_flag,</if>
            <if test="planId != null and planId != ''">plan_id,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="planName != null">plan_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="takingCode != null and takingCode != ''">#{takingCode},</if>
            <if test="takingName != null">#{takingName},</if>
            <if test="takingDate != null">#{takingDate},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="takingType != null and takingType != ''">#{takingType},</if>
            <if test="blindFlag != null and blindFlag != ''">#{blindFlag},</if>
            <if test="frozenFlag != null and frozenFlag != ''">#{frozenFlag},</if>
            <if test="planId != null and planId != ''">#{planId},</if>
            <if test="planCode != null">#{planCode},</if>
            <if test="planName != null">#{planName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmStockTaking" parameterType="WmStockTaking">
        update wm_stock_taking
        <trim prefix="SET" suffixOverrides=",">
            <if test="takingCode != null and takingCode != ''">taking_code = #{takingCode},</if>
            <if test="takingName != null">taking_name = #{takingName},</if>
            <if test="takingDate != null">taking_date = #{takingDate},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="takingType != null and takingType != ''">taking_type = #{takingType},</if>
            <if test="blindFlag != null and blindFlag != ''">blind_flag = #{blindFlag},</if>
            <if test="frozenFlag != null and frozenFlag != ''">frozen_flag = #{frozenFlag},</if>
            <if test="planId != null and planId != ''">plan_id = #{planId},</if>
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where taking_id = #{takingId}
    </update>

    <delete id="deleteWmStockTakingByTakingId" parameterType="Long">
        delete from wm_stock_taking where taking_id = #{takingId}
    </delete>

    <delete id="deleteWmStockTakingByTakingIds" parameterType="String">
        delete from wm_stock_taking where taking_id in 
        <foreach item="takingId" collection="array" open="(" separator="," close=")">
            #{takingId}
        </foreach>
    </delete>
</mapper>