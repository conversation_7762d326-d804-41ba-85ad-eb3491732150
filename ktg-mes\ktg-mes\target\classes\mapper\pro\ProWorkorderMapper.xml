<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProWorkorderMapper">
    
    <resultMap type="ProWorkorder" id="ProWorkorderResult">
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="workorderType"  column="workorder_type" />
        <result property="orderSource"    column="order_source"    />
        <result property="sourceCode"    column="source_code"    />
        <result property="productId"    column="product_id"    />
        <result property="productCode"    column="product_code"    />
        <result property="productName"    column="product_name"    />
        <result property="productSpc"    column="product_spc"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName" column="unit_name" />
        <result property="batchCode" column="batch_code"></result>
        <result property="quantity"    column="quantity"    />
        <result property="quantityProduced"    column="quantity_produced"    />
        <result property="quantityChanged"    column="quantity_changed"    />
        <result property="quantityScheduled"    column="quantity_scheduled"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="vendorId" column="vendor_id"/>
        <result property="vendorCode" column="vendor_code"/>
        <result property="vendorName" column="vendor_name"/>
        <result property="requestDate"    column="request_date"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="cancelDate"    column="cancel_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProWorkorderVo">
        select workorder_id, workorder_code, workorder_name, workorder_type, order_source, source_code, product_id, product_code, product_name, product_spc, unit_of_measure, unit_name, batch_code, quantity,quantity_produced, client_id, client_code, client_name, vendor_id, vendor_code, vendor_name, request_date, parent_id, ancestors, finish_date, cancel_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_workorder
    </sql>

    <select id="selectProWorkorderList" parameterType="ProWorkorder" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        <where>  
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="workorderType != null and workorderType !=''"> and workorder_type = #{workorderType}</if>
            <if test="orderSource != null  and orderSource != ''"> and order_source = #{orderSource}</if>
            <if test="sourceCode != null  and sourceCode != ''"> and source_code = #{sourceCode}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productCode != null  and productCode != ''"> and product_code = #{productCode}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productSpc != null  and productSpc != ''"> and product_spc = #{productSpc}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null and unitName != ''">and unit_name = #{unitName}</if>
            <if test="batchCode !=null and batchCode !=''" >and batch_code = #{batchCode}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="quantityProduced != null "> and quantity_produced = #{quantityProduced}</if>
            <if test="quantityChanged != null "> and quantity_changed = #{quantityChanged}</if>
            <if test="quantityScheduled != null "> and quantity_scheduled = #{quantityScheduled}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="vendorId != null"> and vendor_id = #{vendorId}</if>
            <if test="vendorCode != null and vendorCode !=''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null and vendorName !=''"> and vendor_name like concat('%',#{vendorName},'%')</if>
            <if test="requestDate != null "> and request_date = #{requestDate}</if>
            <if test="finishDate != null "> and finish_date = #{finishDate}</if>
            <if test="cancelDate != null "> and cancel_date = #{cancelDate}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProWorkorderByWorkorderId" parameterType="Long" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where workorder_id = #{workorderId}
    </select>

    <select id="selectProWorkorderListByParentId" parameterType="Long" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where parent_id = #{parentId}
    </select>

    <select id="checkWorkorderCodeUnique" parameterType="ProWorkorder" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where workorder_code = #{workorderCode} limit 1
    </select>

    <insert id="insertProWorkorder" parameterType="ProWorkorder" useGeneratedKeys="true" keyProperty="workorderId">
        insert into pro_workorder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="workorderName != null and workorderName != ''">workorder_name,</if>
            <if test="workorderType != null and workorderType != ''">workorder_type,</if>
            <if test="orderSource != null and orderSource != ''">order_source,</if>
            <if test="sourceCode != null">source_code,</if>
            <if test="productId != null">product_id,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productSpc != null">product_spc,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="batchCode !=null and batchCode !=''" >batch_code,</if>
            <if test="quantity != null">quantity,</if>
            <if test="quantityProduced != null">quantity_produced,</if>
            <if test="quantityChanged != null">quantity_changed,</if>
            <if test="quantityScheduled != null">quantity_scheduled,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="vendorId !=null">vendor_id,</if>
            <if test="vendorCode !=null">vendor_code,</if>
            <if test="vendorName !=null">vendor_name,</if>
            <if test="requestDate != null">request_date,</if>
            <if test="finishDate != null">finish_date,</if>
            <if test="cancelDate != null">cancel_date,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">#{workorderName},</if>
            <if test="workorderType != null and workorderType != ''">#{workorderType},</if>
            <if test="orderSource != null and orderSource != ''">#{orderSource},</if>
            <if test="sourceCode != null">#{sourceCode},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productSpc != null">#{productSpc},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="batchCode !=null and batchCode !=''" >#{batchCode},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="quantityProduced != null">#{quantityProduced},</if>
            <if test="quantityChanged != null">#{quantityChanged},</if>
            <if test="quantityScheduled != null">#{quantityScheduled},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="vendorId !=null">#{vendorId},</if>
            <if test="vendorCode !=null">#{vendorCode},</if>
            <if test="vendorName !=null">#{vendorName},</if>
            <if test="requestDate != null">#{requestDate},</if>
            <if test="finishDate != null">#{finishDate},</if>
            <if test="cancelDate != null">#{cancelDate},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProWorkorder" parameterType="ProWorkorder">
        update pro_workorder
        <trim prefix="SET" suffixOverrides=",">
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">workorder_name = #{workorderName},</if>
            <if test="workorderType != null and workorderType != ''">workorder_type = #{workorderType},</if>
            <if test="orderSource != null and orderSource != ''">order_source = #{orderSource},</if>
            <if test="sourceCode != null">source_code = #{sourceCode},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productSpc != null">product_spc = #{productSpc},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="batchCode !=null and batchCode !=''" >batch_code = #{batchCode},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="quantityProduced != null">quantity_produced = #{quantityProduced},</if>
            <if test="quantityChanged != null">quantity_changed = #{quantityChanged},</if>
            <if test="quantityScheduled != null">quantity_scheduled = #{quantityScheduled},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="vendorId !=null">vendor_id = #{vendorId},</if>
            <if test="vendorCode !=null">vendor_code = #{vendorCode},</if>
            <if test="vendorName !=null">vendor_name = #{vendorName},</if>
            <if test="requestDate != null">request_date = #{requestDate},</if>
            <if test="finishDate != null">finish_date = #{finishDate},</if>
            <if test="cancelDate != null">cancel_date = #{cancelDate},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where workorder_id = #{workorderId}
    </update>

    <delete id="deleteProWorkorderByWorkorderId" parameterType="Long">
        delete from pro_workorder where workorder_id = #{workorderId}
    </delete>

    <delete id="deleteProWorkorderByWorkorderIds" parameterType="String">
        delete from pro_workorder where workorder_id in 
        <foreach item="workorderId" collection="array" open="(" separator="," close=")">
            #{workorderId}
        </foreach>
    </delete>
</mapper>