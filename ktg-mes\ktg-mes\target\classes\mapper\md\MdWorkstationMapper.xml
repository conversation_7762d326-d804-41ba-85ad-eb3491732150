<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdWorkstationMapper">
    
    <resultMap type="MdWorkstation" id="MdWorkstationResult">
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="workstationAddress"    column="workstation_address"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopCode"    column="workshop_code"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdWorkstationVo">
        select workstation_id, workstation_code, workstation_name, workstation_address, workshop_id, workshop_code, workshop_name, process_id, process_code, process_name,warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_workstation
    </sql>

    <select id="selectMdWorkstationList" parameterType="MdWorkstation" resultMap="MdWorkstationResult">
        <include refid="selectMdWorkstationVo"/>
        <where>  
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="workstationAddress != null  and workstationAddress != ''"> and workstation_address = #{workstationAddress}</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="workshopCode != null  and workshopCode != ''"> and workshop_code = #{workshopCode}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
        </where>
    </select>
    
    <select id="selectMdWorkstationByWorkstationId" parameterType="Long" resultMap="MdWorkstationResult">
        <include refid="selectMdWorkstationVo"/>
        where workstation_id = #{workstationId}
    </select>

    <select id="checkWorkStationCodeUnique" parameterType="MdWorkstation" resultMap="MdWorkstationResult">
        <include refid="selectMdWorkstationVo"/>
        where workstation_code = #{workstationCode} limit 1
    </select>

    <select id="checkWorkStationNameUnique" parameterType="MdWorkstation" resultMap="MdWorkstationResult">
        <include refid="selectMdWorkstationVo"/>
        where workstation_name = #{workstationName} limit 1
    </select>

    <insert id="insertMdWorkstation" parameterType="MdWorkstation" useGeneratedKeys="true" keyProperty="workstationId">
        insert into md_workstation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workstationCode != null and workstationCode != ''">workstation_code,</if>
            <if test="workstationName != null and workstationName != ''">workstation_name,</if>
            <if test="workstationAddress != null">workstation_address,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="workshopCode != null">workshop_code,</if>
            <if test="workshopName != null">workshop_name,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workstationCode != null and workstationCode != ''">#{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">#{workstationName},</if>
            <if test="workstationAddress != null">#{workstationAddress},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="workshopCode != null">#{workshopCode},</if>
            <if test="workshopName != null">#{workshopName},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdWorkstation" parameterType="MdWorkstation">
        update md_workstation
        <trim prefix="SET" suffixOverrides=",">
            <if test="workstationCode != null and workstationCode != ''">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">workstation_name = #{workstationName},</if>
            <if test="workstationAddress != null">workstation_address = #{workstationAddress},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="workshopCode != null">workshop_code = #{workshopCode},</if>
            <if test="workshopName != null">workshop_name = #{workshopName},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where workstation_id = #{workstationId}
    </update>

    <delete id="deleteMdWorkstationByWorkstationId" parameterType="Long">
        delete from md_workstation where workstation_id = #{workstationId}
    </delete>

    <delete id="deleteMdWorkstationByWorkstationIds" parameterType="String">
        delete from md_workstation where workstation_id in 
        <foreach item="workstationId" collection="array" open="(" separator="," close=")">
            #{workstationId}
        </foreach>
    </delete>
</mapper>