<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdWorkstationWorkerMapper">
    
    <resultMap type="MdWorkstationWorker" id="MdWorkstationWorkerResult">
        <result property="recordId"    column="record_id"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="postId"    column="post_id"    />
        <result property="postCode"    column="post_code"    />
        <result property="postName"    column="post_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdWorkstationWorkerVo">
        select record_id, workstation_id, post_id, post_code, post_name, quantity, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_workstation_worker
    </sql>

    <select id="selectMdWorkstationWorkerList" parameterType="MdWorkstationWorker" resultMap="MdWorkstationWorkerResult">
        <include refid="selectMdWorkstationWorkerVo"/>
        <where>  
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="postId != null "> and post_id = #{postId}</if>
            <if test="postCode != null  and postCode != ''"> and post_code = #{postCode}</if>
            <if test="postName != null  and postName != ''"> and post_name like concat('%', #{postName}, '%')</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
        </where>
    </select>
    
    <select id="selectMdWorkstationWorkerByRecordId" parameterType="Long" resultMap="MdWorkstationWorkerResult">
        <include refid="selectMdWorkstationWorkerVo"/>
        where record_id = #{recordId}
    </select>

    <select id="checkPostExist" parameterType="MdWorkstationWorker" resultMap="MdWorkstationWorkerResult">
        <include refid="selectMdWorkstationWorkerVo"/>
        where post_code = #{postCode} and workstation_id = #{workstationId} limit 1
    </select>

    <insert id="insertMdWorkstationWorker" parameterType="MdWorkstationWorker" useGeneratedKeys="true" keyProperty="recordId">
        insert into md_workstation_worker
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workstationId != null">workstation_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="postCode != null">post_code,</if>
            <if test="postName != null">post_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workstationId != null">#{workstationId},</if>
            <if test="postId != null">#{postId},</if>
            <if test="postCode != null">#{postCode},</if>
            <if test="postName != null">#{postName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdWorkstationWorker" parameterType="MdWorkstationWorker">
        update md_workstation_worker
        <trim prefix="SET" suffixOverrides=",">
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="postCode != null">post_code = #{postCode},</if>
            <if test="postName != null">post_name = #{postName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteMdWorkstationWorkerByRecordId" parameterType="Long">
        delete from md_workstation_worker where record_id = #{recordId}
    </delete>

    <delete id="deleteMdWorkstationWorkerByRecordIds" parameterType="String">
        delete from md_workstation_worker where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <delete id="deleteByWorkstationId" parameterType="Long">
        delete from md_workstation_worker where workstation_id = #{workstationId}
    </delete>
</mapper>