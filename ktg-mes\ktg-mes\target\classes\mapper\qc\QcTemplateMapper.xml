<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcTemplateMapper">
    
    <resultMap type="QcTemplate" id="QcTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="templateCode"    column="template_code"    />
        <result property="templateName"    column="template_name"    />
        <result property="qcTypes"    column="qc_types"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcTemplateVo">
        select template_id, template_code, template_name, qc_types, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from qc_template
    </sql>

    <select id="selectQcTemplateList" parameterType="QcTemplate" resultMap="QcTemplateResult">
        <include refid="selectQcTemplateVo"/>
        <where>  
            <if test="templateCode != null  and templateCode != ''"> and template_code = #{templateCode}</if>
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="qcTypes != null  and qcTypes != ''"> and qc_types = #{qcTypes}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
        </where>
    </select>
    
    <select id="selectQcTemplateByTemplateId" parameterType="Long" resultMap="QcTemplateResult">
        <include refid="selectQcTemplateVo"/>
        where template_id = #{templateId}
    </select>

    <select id="checkTemplateCodeUnique" parameterType="QcTemplate" resultMap="QcTemplateResult">
        <include refid="selectQcTemplateVo"/>
        where template_code = #{templateCode}
    </select>

    <select id="selectQcTemplateByProductAndQcType" parameterType="QcTemplate" resultMap="QcTemplateResult">
        select t.template_id, template_code, template_name, qc_types, enable_flag, t.remark
        from qc_template t
        left join qc_template_product tl on t.template_id = tl.template_id
        where t.qc_types like concat('%',#{qcTypes},'%') and tl.item_id = #{itemId} and t.enable_flag = 'Y'
        limit 1;
    </select>

    <select id="findTemplateByProductIdAndQcType" parameterType="QcMobParam" resultMap="QcTemplateResult">
        select t.*
        from  qc_template t
                  left join qc_template_product p
                            on t.template_id = p.template_id
        where p.item_id = #{itemId}
          and t.qc_types like concat('%',#{qcType},'%')
          and t.enable_flag = 'Y'
        limit 1
    </select>

    <insert id="insertQcTemplate" parameterType="QcTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into qc_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateCode != null and templateCode != ''">template_code,</if>
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="qcTypes != null and qcTypes != ''">qc_types,</if>
            <if test="enableFlag != null">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateCode != null and templateCode != ''">#{templateCode},</if>
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="qcTypes != null and qcTypes != ''">#{qcTypes},</if>
            <if test="enableFlag != null">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQcTemplate" parameterType="QcTemplate">
        update qc_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateCode != null and templateCode != ''">template_code = #{templateCode},</if>
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="qcTypes != null and qcTypes != ''">qc_types = #{qcTypes},</if>
            <if test="enableFlag != null">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteQcTemplateByTemplateId" parameterType="Long">
        delete from qc_template where template_id = #{templateId}
    </delete>

    <delete id="deleteQcTemplateByTemplateIds" parameterType="String">
        delete from qc_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper>