<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmSalesNoticeLineMapper">
    
    <resultMap type="WmSalesNoticeLine" id="WmSalesNoticeLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName"    column="unit_name"    />
        <result property="batchId"    column="batch_id"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="quantitySales"    column="quantity_sales"    />
        <result property="oqcCheck"    column="oqc_check"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmSalesNoticeLineVo">
        select line_id, notice_id, item_id, item_code, item_name, specification, unit_of_measure, unit_name, batch_id, batch_code, quantity_sales, oqc_check, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_sales_notice_line
    </sql>

    <select id="selectWmSalesNoticeLineList" parameterType="WmSalesNoticeLine" resultMap="WmSalesNoticeLineResult">
        <include refid="selectWmSalesNoticeLineVo"/>
        <where>  
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="batchId != null "> and batch_id = #{batchId}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="quantitySales != null "> and quantity_sales = #{quantitySales}</if>
            <if test="oqcCheck != null  and oqcCheck != ''"> and oqc_check = #{oqcCheck}</if>
        </where>
    </select>
    
    <select id="selectWmSalesNoticeLineByLineId" parameterType="Long" resultMap="WmSalesNoticeLineResult">
        <include refid="selectWmSalesNoticeLineVo"/>
        where line_id = #{lineId}
    </select>
        
    <insert id="insertWmSalesNoticeLine" parameterType="WmSalesNoticeLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into wm_sales_notice_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="quantitySales != null">quantity_sales,</if>
            <if test="oqcCheck != null">oqc_check,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="quantitySales != null">#{quantitySales},</if>
            <if test="oqcCheck != null">#{oqcCheck},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmSalesNoticeLine" parameterType="WmSalesNoticeLine">
        update wm_sales_notice_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="quantitySales != null">quantity_sales = #{quantitySales},</if>
            <if test="oqcCheck != null">oqc_check = #{oqcCheck},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteWmSalesNoticeLineByLineId" parameterType="Long">
        delete from wm_sales_notice_line where line_id = #{lineId}
    </delete>

    <delete id="deleteWmSalesNoticeLineByLineIds" parameterType="String">
        delete from wm_sales_notice_line where line_id in 
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>
</mapper>