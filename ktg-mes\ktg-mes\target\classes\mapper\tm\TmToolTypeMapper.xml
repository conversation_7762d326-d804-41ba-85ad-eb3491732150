<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.tm.mapper.TmToolTypeMapper">
    
    <resultMap type="TmToolType" id="TmToolTypeResult">
        <result property="toolTypeId"    column="tool_type_id"    />
        <result property="toolTypeCode"    column="tool_type_code"    />
        <result property="toolTypeName"    column="tool_type_name"    />
        <result property="codeFlag"    column="code_flag"    />
        <result property="maintenType"    column="mainten_type"    />
        <result property="maintenPeriod"    column="mainten_period"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTmToolTypeVo">
        select tool_type_id, tool_type_code, tool_type_name, code_flag, mainten_type, mainten_period, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from tm_tool_type
    </sql>

    <select id="selectTmToolTypeList" parameterType="TmToolType" resultMap="TmToolTypeResult">
        <include refid="selectTmToolTypeVo"/>
        <where>  
            <if test="toolTypeCode != null  and toolTypeCode != ''"> and tool_type_code = #{toolTypeCode}</if>
            <if test="toolTypeName != null  and toolTypeName != ''"> and tool_type_name like concat('%', #{toolTypeName}, '%')</if>
            <if test="codeFlag != null  and codeFlag != ''"> and code_flag = #{codeFlag}</if>
            <if test="maintenType != null  and maintenType != ''"> and mainten_type = #{maintenType}</if>
            <if test="maintenPeriod != null "> and mainten_period = #{maintenPeriod}</if>
        </where>
    </select>
    
    <select id="selectTmToolTypeByToolTypeId" parameterType="Long" resultMap="TmToolTypeResult">
        <include refid="selectTmToolTypeVo"/>
        where tool_type_id = #{toolTypeId}
    </select>

    <select id="checkToolTypeCodeUnique" parameterType="TmToolType" resultMap="TmToolTypeResult">
        <include refid="selectTmToolTypeVo"/>
        where tool_type_code = #{toolTypeCode} limit 1
    </select>

    <select id="checkToolTypeNameUnique" parameterType="TmToolType" resultMap="TmToolTypeResult">
        <include refid="selectTmToolTypeVo"/>
        where tool_type_name = #{toolTypeName} limit 1
    </select>

    <insert id="insertTmToolType" parameterType="TmToolType" useGeneratedKeys="true" keyProperty="toolTypeId">
        insert into tm_tool_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="toolTypeCode != null and toolTypeCode != ''">tool_type_code,</if>
            <if test="toolTypeName != null and toolTypeName != ''">tool_type_name,</if>
            <if test="codeFlag != null and codeFlag != ''">code_flag,</if>
            <if test="maintenType != null">mainten_type,</if>
            <if test="maintenPeriod != null">mainten_period,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="toolTypeCode != null and toolTypeCode != ''">#{toolTypeCode},</if>
            <if test="toolTypeName != null and toolTypeName != ''">#{toolTypeName},</if>
            <if test="codeFlag != null and codeFlag != ''">#{codeFlag},</if>
            <if test="maintenType != null">#{maintenType},</if>
            <if test="maintenPeriod != null">#{maintenPeriod},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTmToolType" parameterType="TmToolType">
        update tm_tool_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="toolTypeCode != null and toolTypeCode != ''">tool_type_code = #{toolTypeCode},</if>
            <if test="toolTypeName != null and toolTypeName != ''">tool_type_name = #{toolTypeName},</if>
            <if test="codeFlag != null and codeFlag != ''">code_flag = #{codeFlag},</if>
            <if test="maintenType != null">mainten_type = #{maintenType},</if>
            <if test="maintenPeriod != null">mainten_period = #{maintenPeriod},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where tool_type_id = #{toolTypeId}
    </update>

    <delete id="deleteTmToolTypeByToolTypeId" parameterType="Long">
        delete from tm_tool_type where tool_type_id = #{toolTypeId}
    </delete>

    <delete id="deleteTmToolTypeByToolTypeIds" parameterType="String">
        delete from tm_tool_type where tool_type_id in 
        <foreach item="toolTypeId" collection="array" open="(" separator="," close=")">
            #{toolTypeId}
        </foreach>
    </delete>
</mapper>