<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTransOrderMapper">
    
    <resultMap type="ProTransOrder" id="ProTransOrderResult">
        <result property="transOrderId"    column="trans_order_id"    />
        <result property="transOrderCode"    column="trans_order_code"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="barCodeUrl" column="barcode_url" ></result>
        <result property="quantityTransfered"    column="quantity_transfered"    />
        <result property="produceDate"    column="produce_date"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTransOrderVo">
        select trans_order_id, trans_order_code, task_id, task_code, workstation_id, workstation_code, workstation_name, process_id, process_code, process_name, workorder_id, workorder_code, workorder_name, batch_code, item_id, item_code, item_name, specification, unit_of_measure,barcode_url, quantity_transfered, produce_date, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_trans_order
    </sql>

    <select id="selectProTransOrderList" parameterType="ProTransOrder" resultMap="ProTransOrderResult">
        <include refid="selectProTransOrderVo"/>
        <where>  
            <if test="transOrderCode != null  and transOrderCode != ''"> and trans_order_code = #{transOrderCode}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="barCodeUrl != null  and barCodeUrl != ''"> and barcode_url = #{barCodeUrl}</if>
            <if test="quantityTransfered != null "> and quantity_transfered = #{quantityTransfered}</if>
            <if test="produceDate != null "> and produce_date = #{produceDate}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProTransOrderByTransOrderId" parameterType="Long" resultMap="ProTransOrderResult">
        <include refid="selectProTransOrderVo"/>
        where trans_order_id = #{transOrderId}
    </select>
        
    <insert id="insertProTransOrder" parameterType="ProTransOrder" useGeneratedKeys="true" keyProperty="transOrderId">
        insert into pro_trans_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="transOrderCode != null">trans_order_code,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null">task_code,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="workorderName != null">workorder_name,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="barCodeUrl != null and barCodeUrl != ''">barcode_url,</if>
            <if test="quantityTransfered != null">quantity_transfered,</if>
            <if test="produceDate != null">produce_date,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="transOrderCode != null">#{transOrderCode},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="workorderName != null">#{workorderName},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="barCodeUrl != null and barCodeUrl != ''">#{barCodeUrl},</if>
            <if test="quantityTransfered != null">#{quantityTransfered},</if>
            <if test="produceDate != null">#{produceDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProTransOrder" parameterType="ProTransOrder">
        update pro_trans_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="transOrderCode != null">trans_order_code = #{transOrderCode},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null">task_code = #{taskCode},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null">workorder_name = #{workorderName},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="barCodeUrl != null and barCodeUrl != ''">barcode_url = #{barCodeUrl},</if>
            <if test="quantityTransfered != null">quantity_transfered = #{quantityTransfered},</if>
            <if test="produceDate != null">produce_date = #{produceDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where trans_order_id = #{transOrderId}
    </update>

    <delete id="deleteProTransOrderByTransOrderId" parameterType="Long">
        delete from pro_trans_order where trans_order_id = #{transOrderId}
    </delete>

    <delete id="deleteProTransOrderByTransOrderIds" parameterType="String">
        delete from pro_trans_order where trans_order_id in 
        <foreach item="transOrderId" collection="array" open="(" separator="," close=")">
            #{transOrderId}
        </foreach>
    </delete>
</mapper>