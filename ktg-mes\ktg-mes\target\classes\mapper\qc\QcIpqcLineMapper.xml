<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcIpqcLineMapper">
    
    <resultMap type="QcIpqcLine" id="QcIpqcLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="ipqcId"    column="ipqc_id"    />
        <result property="indexId"    column="index_id"    />
        <result property="indexCode"    column="index_code"    />
        <result property="indexName"    column="index_name"    />
        <result property="indexType"    column="index_type"    />
        <result property="qcTool"    column="qc_tool"    />
        <result property="checkMethod"    column="check_method"    />
        <result property="standerVal"    column="stander_val"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="thresholdMax"    column="threshold_max"    />
        <result property="thresholdMin"    column="threshold_min"    />
        <result property="crQuantity"    column="cr_quantity"    />
        <result property="majQuantity"    column="maj_quantity"    />
        <result property="minQuantity"    column="min_quantity"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcIpqcLineVo">
        select line_id, ipqc_id, index_id, index_code, index_name, index_type, qc_tool, check_method, stander_val, unit_of_measure, threshold_max, threshold_min, cr_quantity, maj_quantity, min_quantity, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from qc_ipqc_line
    </sql>

    <select id="selectQcIpqcLineList" parameterType="QcIpqcLine" resultMap="QcIpqcLineResult">
        <include refid="selectQcIpqcLineVo"/>
        <where>  
            <if test="ipqcId != null "> and ipqc_id = #{ipqcId}</if>
            <if test="indexId != null "> and index_id = #{indexId}</if>
            <if test="indexCode != null  and indexCode != ''"> and index_code = #{indexCode}</if>
            <if test="indexName != null  and indexName != ''"> and index_name like concat('%', #{indexName}, '%')</if>
            <if test="indexType != null  and indexType != ''"> and index_type = #{indexType}</if>
            <if test="qcTool != null  and qcTool != ''"> and qc_tool = #{qcTool}</if>
            <if test="checkMethod != null  and checkMethod != ''"> and check_method = #{checkMethod}</if>
            <if test="standerVal != null "> and stander_val = #{standerVal}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="thresholdMax != null "> and threshold_max = #{thresholdMax}</if>
            <if test="thresholdMin != null "> and threshold_min = #{thresholdMin}</if>
            <if test="crQuantity != null "> and cr_quantity = #{crQuantity}</if>
            <if test="majQuantity != null "> and maj_quantity = #{majQuantity}</if>
            <if test="minQuantity != null "> and min_quantity = #{minQuantity}</if>
        </where>
    </select>
    
    <select id="selectQcIpqcLineByLineId" parameterType="Long" resultMap="QcIpqcLineResult">
        <include refid="selectQcIpqcLineVo"/>
        where line_id = #{lineId}
    </select>
        
    <insert id="insertQcIpqcLine" parameterType="QcIpqcLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into qc_ipqc_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ipqcId != null">ipqc_id,</if>
            <if test="indexId != null">index_id,</if>
            <if test="indexCode != null">index_code,</if>
            <if test="indexName != null">index_name,</if>
            <if test="indexType != null">index_type,</if>
            <if test="qcTool != null">qc_tool,</if>
            <if test="checkMethod != null">check_method,</if>
            <if test="standerVal != null">stander_val,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="thresholdMax != null">threshold_max,</if>
            <if test="thresholdMin != null">threshold_min,</if>
            <if test="crQuantity != null">cr_quantity,</if>
            <if test="majQuantity != null">maj_quantity,</if>
            <if test="minQuantity != null">min_quantity,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ipqcId != null">#{ipqcId},</if>
            <if test="indexId != null">#{indexId},</if>
            <if test="indexCode != null">#{indexCode},</if>
            <if test="indexName != null">#{indexName},</if>
            <if test="indexType != null">#{indexType},</if>
            <if test="qcTool != null">#{qcTool},</if>
            <if test="checkMethod != null">#{checkMethod},</if>
            <if test="standerVal != null">#{standerVal},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="thresholdMax != null">#{thresholdMax},</if>
            <if test="thresholdMin != null">#{thresholdMin},</if>
            <if test="crQuantity != null">#{crQuantity},</if>
            <if test="majQuantity != null">#{majQuantity},</if>
            <if test="minQuantity != null">#{minQuantity},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQcIpqcLine" parameterType="QcIpqcLine">
        update qc_ipqc_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="ipqcId != null">ipqc_id = #{ipqcId},</if>
            <if test="indexId != null">index_id = #{indexId},</if>
            <if test="indexCode != null">index_code = #{indexCode},</if>
            <if test="indexName != null">index_name = #{indexName},</if>
            <if test="indexType != null">index_type = #{indexType},</if>
            <if test="qcTool != null">qc_tool = #{qcTool},</if>
            <if test="checkMethod != null">check_method = #{checkMethod},</if>
            <if test="standerVal != null">stander_val = #{standerVal},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="thresholdMax != null">threshold_max = #{thresholdMax},</if>
            <if test="thresholdMin != null">threshold_min = #{thresholdMin},</if>
            <if test="crQuantity != null">cr_quantity = #{crQuantity},</if>
            <if test="majQuantity != null">maj_quantity = #{majQuantity},</if>
            <if test="minQuantity != null">min_quantity = #{minQuantity},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <update id="updateCrMajMinQuantity" parameterType="QcIpqcLine">
        UPDATE qc_ipqc_line a
            INNER JOIN (
            SELECT SUM(CASE WHEN defect_level = 'CR' THEN defect_quantity ELSE 0 END ) AS cr_quantity,
            SUM(CASE WHEN defect_level = 'MAJ' THEN defect_quantity ELSE 0 END) AS maj_quantity,
            SUM(CASE WHEN defect_level = 'MIN' THEN defect_quantity ELSE 0 END) AS min_quantity,
            qid.`qc_id` as 'ipqc_id',
            qid.`line_id`
            FROM qc_defect_record qid
            WHERE qid.qc_id = #{ipqcId}
            AND qid.line_id = #{lineId}
            and qid.qc_type = 'IPQC'
            GROUP BY qid.qc_id,qid.line_id
            )b
        ON a.ipqc_id = b.ipqc_id AND a.line_id = b.line_id
            SET a.cr_quantity=b.cr_quantity,a.maj_quantity=b.maj_quantity,a.min_quantity=b.min_quantity
        WHERE a.ipqc_id = #{ipqcId}
          AND a.line_id = #{lineId}
    </update>

    <delete id="deleteQcIpqcLineByLineId" parameterType="Long">
        delete from qc_ipqc_line where line_id = #{lineId}
    </delete>

    <delete id="deleteQcIpqcLineByLineIds" parameterType="String">
        delete from qc_ipqc_line where line_id in 
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteByIpqcId" parameterType="Long">
        delete from qc_ipqc_line where ipqc_id = #{ipqcId}
    </delete>

</mapper>