<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmArrivalNoticeLineMapper">
    
    <resultMap type="WmArrivalNoticeLine" id="WmArrivalNoticeLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName" column="unit_name" />
        <result property="quantityArrival"    column="quantity_arrival"    />
        <result property="quantityQuanlified"    column="quantity_quanlified"    />
        <result property="iqcCheck"    column="iqc_check"    />
        <result property="iqcId"    column="iqc_id"    />
        <result property="iqcCode"    column="iqc_code"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmArrivalNoticeLineVo">
        select line_id, notice_id, item_id, item_code, item_name, specification, unit_of_measure, unit_name, quantity_arrival, quantity_quanlified, iqc_check, iqc_id, iqc_code, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_arrival_notice_line
    </sql>

    <select id="selectWmArrivalNoticeLineList" parameterType="WmArrivalNoticeLine" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        <where>  
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null and unitName != ''">and unit_name = #{unitName}</if>
            <if test="quantityArrival != null "> and quantity_arrival = #{quantityArrival}</if>
            <if test="quantityQuanlified != null "> and quantity_quanlified = #{quantityQuanlified}</if>
            <if test="iqcCheck != null  and iqcCheck != ''"> and iqc_check = #{iqcCheck}</if>
            <if test="iqcId != null "> and iqc_id = #{iqcId}</if>
            <if test="iqcCode != null  and iqcCode != ''"> and iqc_code = #{iqcCode}</if>
        </where>
    </select>
    
    <select id="selectWmArrivalNoticeLineByLineId" parameterType="Long" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        where line_id = #{lineId}
    </select>

    <select id="selectUncheckedLine" parameterType="Long" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        where notice_id = #{noticeId}
        and iqc_check = 'Y'
        and iqc_id is null
    </select>
        
    <insert id="insertWmArrivalNoticeLine" parameterType="WmArrivalNoticeLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into wm_arrival_notice_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="quantityArrival != null">quantity_arrival,</if>
            <if test="quantityQuanlified != null">quantity_quanlified,</if>
            <if test="iqcCheck != null">iqc_check,</if>
            <if test="iqcId != null">iqc_id,</if>
            <if test="iqcCode != null">iqc_code,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="quantityArrival != null">#{quantityArrival},</if>
            <if test="quantityQuanlified != null">#{quantityQuanlified},</if>
            <if test="iqcCheck != null">#{iqcCheck},</if>
            <if test="iqcId != null">#{iqcId},</if>
            <if test="iqcCode != null">#{iqcCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmArrivalNoticeLine" parameterType="WmArrivalNoticeLine">
        update wm_arrival_notice_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="quantityArrival != null">quantity_arrival = #{quantityArrival},</if>
            <if test="quantityQuanlified != null">quantity_quanlified = #{quantityQuanlified},</if>
            <if test="iqcCheck != null">iqc_check = #{iqcCheck},</if>
            <if test="iqcId != null">iqc_id = #{iqcId},</if>
            <if test="iqcCode != null">iqc_code = #{iqcCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteWmArrivalNoticeLineByLineId" parameterType="Long">
        delete from wm_arrival_notice_line where line_id = #{lineId}
    </delete>

    <delete id="deleteWmArrivalNoticeLineByLineIds" parameterType="String">
        delete from wm_arrival_notice_line where line_id in 
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteByNoticeId" parameterType="Long">
        delete from wm_arrival_notice_line where notice_id = #{noticeId}
    </delete>

</mapper>