<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmWarehouseMapper">
    
    <resultMap type="WmWarehouse" id="WmWarehouseResult">
        <id property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="location"    column="location"    />
        <result property="area"    column="area"    />
        <result property="userId" column="user_id" />
        <result property="userName" column="user_name" />
        <result property="charge"    column="charge"    />
        <result property="frozenFlag"    column="frozen_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <collection  property="children"   javaType="java.util.List"  resultMap="WmStorageLocationResult" />
    </resultMap>

    <resultMap type="WmStorageLocation" id="WmStorageLocationResult">
        <id property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="area"    column="area"    />
        <result property="areaFlag"    column="area_flag"    />
        <collection  property="children"   javaType="java.util.List"  resultMap="WmStorageAreaResult" />
    </resultMap>

    <resultMap type="WmStorageArea" id="WmStorageAreaResult">
        <id property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="area"    column="area"    />
        <result property="maxLoa"    column="max_loa"    />
        <result property="positionX"    column="position_x"    />
        <result property="positionY"    column="position_y"    />
        <result property="positionZ"    column="position_z"    />
    </resultMap>


    <sql id="selectWmWarehouseVo">
        select warehouse_id, warehouse_code, warehouse_name, location, area, user_id, user_name, charge, frozen_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_warehouse
    </sql>

    <select id="selectWmWarehouseList" parameterType="WmWarehouse" resultMap="WmWarehouseResult">
        <include refid="selectWmWarehouseVo"/>
        <where>  
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="area != null "> and area = #{area}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="charge != null  and charge != ''"> and charge = #{charge}</if>
        </where>
    </select>

    <select id="getTreeList" resultMap="WmWarehouseResult">
        SELECT w.warehouse_id, warehouse_code, warehouse_name, location, w.area, w.charge,w.frozen_flag, w.remark, w.attr1, w.attr2, w.attr3, w.attr4, w.create_by, w.create_time, w.update_by, w.update_time,
              l.location_id, location_code, location_name,  area_flag,
              area_id, area_code, area_name, max_loa, position_x, position_y, position_z
        FROM wm_warehouse w
          LEFT JOIN wm_storage_location l ON w.warehouse_id = l.warehouse_id
          LEFT JOIN wm_storage_area a ON l.location_id = a.location_id AND a.enable_flag = 'Y'
    </select>
    
    <select id="selectWmWarehouseByWarehouseId" parameterType="Long" resultMap="WmWarehouseResult">
        <include refid="selectWmWarehouseVo"/>
        where warehouse_id = #{warehouseId}
    </select>

    <select id="selectWmWarehouseByWarehouseCode" parameterType="String" resultMap="WmWarehouseResult">
        <include refid="selectWmWarehouseVo"/>
        where warehouse_code = #{warehouseCode} limit 1
    </select>

    <select id="checkWarehouseCodeUnique" parameterType="WmWarehouse" resultMap="WmWarehouseResult">
        <include refid="selectWmWarehouseVo"/>
        where warehouse_code = #{warehouseCode} limit 1
    </select>

    <select id="checkWarehouseNameUnique" parameterType="WmWarehouse" resultMap="WmWarehouseResult">
        <include refid="selectWmWarehouseVo"/>
        where warehouse_name = #{warehouseName} limit 1
    </select>
    <select id="selectByWarehouseId" resultType="com.ktg.mes.wm.domain.WmWarehouse" resultMap="WmWarehouseResult">
        <include refid="selectWmWarehouseVo"/>
        where warehouse_id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertWmWarehouse" parameterType="WmWarehouse" useGeneratedKeys="true" keyProperty="warehouseId">
        insert into wm_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="location != null">location,</if>
            <if test="area != null">area,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="charge != null">charge,</if>
            <if test="frozenFlag != null">frozen_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">#{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="location != null">#{location},</if>
            <if test="area != null">#{area},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="charge != null">#{charge},</if>
            <if test="frozenFlag != null">#{frozenFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmWarehouse" parameterType="WmWarehouse">
        update wm_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="location != null">location = #{location},</if>
            <if test="area != null">area = #{area},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="charge != null">charge = #{charge},</if>
            <if test="frozenFlag != null">frozen_flag = #{frozenFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where warehouse_id = #{warehouseId}
    </update>

    <delete id="deleteWmWarehouseByWarehouseId" parameterType="Long">
        delete from wm_warehouse where warehouse_id = #{warehouseId}
    </delete>

    <delete id="deleteWmWarehouseByWarehouseIds" parameterType="String">
        delete from wm_warehouse where warehouse_id in 
        <foreach item="warehouseId" collection="array" open="(" separator="," close=")">
            #{warehouseId}
        </foreach>
    </delete>
</mapper>