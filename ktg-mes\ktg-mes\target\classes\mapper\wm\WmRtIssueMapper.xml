<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmRtIssueMapper">
    
    <resultMap type="WmRtIssue" id="WmRtIssueResult">
        <result property="rtId"    column="rt_id"    />
        <result property="rtCode"    column="rt_code"    />
        <result property="rtName"    column="rt_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="rtType"    column="rt_type"    />
        <result property="rtDate"    column="rt_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="RtIssueTxBean"  id="RtIssueTxBeanResult">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="unitName" column="unit_name"></result>
        <result property="batchId" column="batch_id"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="recptDate" column="recpt_date"></result>
    </resultMap>

    <sql id="selectWmRtIssueVo">
        select rt_id, rt_code, rt_name, workorder_id, workorder_code, workstation_id, workstation_code, workstation_name, rt_type, rt_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_rt_issue
    </sql>

    <select id="selectWmRtIssueList" parameterType="WmRtIssue" resultMap="WmRtIssueResult">
        <include refid="selectWmRtIssueVo"/>
        <where>  
            <if test="rtCode != null  and rtCode != ''"> and rt_code = #{rtCode}</if>
            <if test="rtName != null  and rtName != ''"> and rt_name like concat('%', #{rtName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="rtType != null  and rtType != ''"> and rt_type = #{rtType}</if>
            <if test="rtDate != null "> and rt_date = #{rtDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmRtIssueByRtId" parameterType="Long" resultMap="WmRtIssueResult">
        <include refid="selectWmRtIssueVo"/>
        where rt_id = #{rtId}
    </select>

    <select id="checkUnique" parameterType="WmRtIssue" resultMap="WmRtIssueResult">
        <include refid="selectWmRtIssueVo"/>
        where rt_code = #{rtCode}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="RtIssueTxBeanResult">
        SELECT ird.material_stock_id,
               irl.`item_id`,
               irl.`item_code`,
               irl.`item_name`,
               irl.`specification`,
               irl.`unit_of_measure`,
               irl.`unit_name`,
               ird.`batch_id`,
               ird.`batch_code`,
               ird.`warehouse_id`,ird.`warehouse_code`,ird.`warehouse_name`,
               ird.`location_id`,ird.`location_code`,ird.`location_name`,
               ird.`area_id`,ird.`area_code`,ird.`area_name`,
               'RT_ISSUE' AS source_doc_type,ir.`rt_id` AS source_doc_id,
               ir.`rt_code` AS source_doc_code,
               irl.`line_id` AS source_doc_line_id,
               ird.quantity AS transaction_quantity,
               ir.rt_date as recptDate
        FROM wm_rt_issue ir
                 LEFT JOIN wm_rt_issue_line irl
                           ON ir.rt_id = irl.rt_id
                 left join wm_rt_issue_detail ird
                           on irl.line_id = ird.line_id
                 left join wm_material_stock ms
                           on irl.material_stock_id = ms.material_stock_id
        WHERE ir.rt_id = #{rtId}
    </select>

    <insert id="insertWmRtIssue" parameterType="WmRtIssue" useGeneratedKeys="true" keyProperty="rtId">
        insert into wm_rt_issue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">rt_code,</if>
            <if test="rtName != null">rt_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="rtType != null">rt_type,</if>
            <if test="rtDate != null">rt_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">#{rtCode},</if>
            <if test="rtName != null">#{rtName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="rtType != null">#{rtType},</if>
            <if test="rtDate != null">#{rtDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmRtIssue" parameterType="WmRtIssue">
        update wm_rt_issue
        <trim prefix="SET" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">rt_code = #{rtCode},</if>
            <if test="rtName != null">rt_name = #{rtName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="rtType != null">rt_type = #{rtType},</if>
            <if test="rtDate != null">rt_date = #{rtDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rt_id = #{rtId}
    </update>

    <delete id="deleteWmRtIssueByRtId" parameterType="Long">
        delete from wm_rt_issue where rt_id = #{rtId}
    </delete>

    <delete id="deleteWmRtIssueByRtIds" parameterType="String">
        delete from wm_rt_issue where rt_id in 
        <foreach item="rtId" collection="array" open="(" separator="," close=")">
            #{rtId}
        </foreach>
    </delete>
</mapper>