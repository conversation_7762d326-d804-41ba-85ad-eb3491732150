<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.dv.mapper.DvCheckPlanMapper">
    
    <resultMap type="DvCheckPlan" id="DvCheckPlanResult">
        <result property="planId"    column="plan_id"    />
        <result property="planCode"    column="plan_code"    />
        <result property="planName"    column="plan_name"    />
        <result property="planType"    column="plan_type"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="cycleType"    column="cycle_type"    />
        <result property="cycleCount"    column="cycle_count"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDvCheckPlanVo">
        select plan_id, plan_code, plan_name,plan_type, start_date, end_date, cycle_type, cycle_count,status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from dv_check_plan
    </sql>

    <select id="selectDvCheckPlanList" parameterType="DvCheckPlan" resultMap="DvCheckPlanResult">
        <include refid="selectDvCheckPlanVo"/>
        <where>  
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="cycleType != null  and cycleType != ''"> and cycle_type = #{cycleType}</if>
            <if test="cycleCount != null "> and cycle_count = #{cycleCount}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDvCheckPlanByPlanId" parameterType="Long" resultMap="DvCheckPlanResult">
        <include refid="selectDvCheckPlanVo"/>
        where plan_id = #{planId}
    </select>

    <select id="checkPlanCodeUnique" parameterType="DvCheckPlan" resultMap="DvCheckPlanResult">
        <include refid="selectDvCheckPlanVo"/>
        where plan_code = #{planCode}
    </select>
    <select id="getByIds" resultType="com.ktg.mes.dv.domain.DvCheckPlan" resultMap="DvCheckPlanResult">
        <include refid="selectDvCheckPlanVo"/>
        where plan_type = #{planType}
        and plan_id in
            <foreach collection="planIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

    <select id="selectDvCheckPlanByMachineryCodeAndType" resultMap="DvCheckPlanResult">
        select pl.plan_id, pl.plan_code, pl.plan_name,pl.plan_type, pl.start_date, pl.end_date, pl.cycle_type, pl.cycle_count,pl.status, pl.remark,  pl.create_time, pl.update_by, pl.update_time
        from dv_check_plan pl
            left join dv_check_machinery dv
        on pl.plan_id = dv.plan_id
        where plan_type = #{planType}
        and dv.machinery_code = #{machineryCode}
        limit 1
    </select>

    <insert id="insertDvCheckPlan" parameterType="DvCheckPlan" useGeneratedKeys="true" keyProperty="planId">
        insert into dv_check_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planCode != null and planCode != ''">plan_code,</if>
            <if test="planName != null">plan_name,</if>
            <if test="planType != null  and planType != ''">plan_type,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="cycleType != null">cycle_type,</if>
            <if test="cycleCount != null">cycle_count,</if>
            <if test="status != null and status !=''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planCode != null and planCode != ''">#{planCode},</if>
            <if test="planName != null">#{planName},</if>
            <if test="planType != null  and planType != ''">#{planType},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="cycleType != null">#{cycleType},</if>
            <if test="cycleCount != null">#{cycleCount},</if>
            <if test="status != null and status !=''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDvCheckPlan" parameterType="DvCheckPlan">
        update dv_check_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planType != null  and planType != ''">plan_type = #{planType},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="cycleType != null">cycle_type = #{cycleType},</if>
            <if test="cycleCount != null">cycle_count = #{cycleCount},</if>
            <if test="status != null and status !=''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where plan_id = #{planId}
    </update>

    <delete id="deleteDvCheckPlanByPlanId" parameterType="Long">
        delete from dv_check_plan where plan_id = #{planId}
    </delete>

    <delete id="deleteDvCheckPlanByPlanIds" parameterType="String">
        delete from dv_check_plan where plan_id in 
        <foreach item="planId" collection="array" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>
</mapper>