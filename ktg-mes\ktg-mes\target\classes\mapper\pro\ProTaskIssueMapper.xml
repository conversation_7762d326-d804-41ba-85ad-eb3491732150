<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskIssueMapper">
    
    <resultMap type="ProTaskIssue" id="ProTaskIssueResult">
        <result property="recordId"    column="record_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="sourceDocId"    column="source_doc_id"    />
        <result property="sourceDocCode"    column="source_doc_code"    />
        <result property="sourceDocType"    column="source_doc_type"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="sourceLineId"    column="source_line_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="quantityIssued"    column="quantity_issued"    />
        <result property="quantityAvailable"    column="quantity_available"    />
        <result property="quantityUsed"    column="quantity_used"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTaskIssueVo">
        select record_id, task_id, workorder_id, workstation_id, source_doc_id, source_doc_code, source_doc_type, batch_code, source_line_id, item_id, item_code, item_name, specification, unit_of_measure, quantity_issued, quantity_available, quantity_used, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_task_issue
    </sql>

    <select id="selectProTaskIssueList" parameterType="ProTaskIssue" resultMap="ProTaskIssueResult">
        <include refid="selectProTaskIssueVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="sourceDocId != null "> and source_doc_id = #{sourceDocId}</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and source_doc_code = #{sourceDocCode}</if>
            <if test="sourceDocType != null  and sourceDocType != ''"> and source_doc_type = #{sourceDocType}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="sourceLineId != null "> and source_line_id = #{sourceLineId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="quantityIssued != null "> and quantity_issued = #{quantityIssued}</if>
            <if test="quantityAvailable != null "> and quantity_available = #{quantityAvailable}</if>
            <if test="quantityUsed != null "> and quantity_used = #{quantityUsed}</if>
        </where>
    </select>
    
    <select id="selectProTaskIssueByRecordId" parameterType="Long" resultMap="ProTaskIssueResult">
        <include refid="selectProTaskIssueVo"/>
        where record_id = #{recordId}
    </select>

    <select id="checkUnique" parameterType="ProTaskIssue" resultMap="ProTaskIssueResult">
        <include refid="selectProTaskIssueVo"/>
        where workstation_id = #{workstationId}
        and task_id = #{taskId}
        and source_doc_type = #{sourceDocType}
        and source_line_id = #{sourceLineId}
        limit 1
    </select>

    <insert id="insertProTaskIssue" parameterType="ProTaskIssue" useGeneratedKeys="true" keyProperty="recordId">
        insert into pro_task_issue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="sourceDocId != null">source_doc_id,</if>
            <if test="sourceDocCode != null">source_doc_code,</if>
            <if test="sourceDocType != null">source_doc_type,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="sourceLineId != null">source_line_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="quantityIssued != null">quantity_issued,</if>
            <if test="quantityAvailable != null">quantity_available,</if>
            <if test="quantityUsed != null">quantity_used,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="sourceDocId != null">#{sourceDocId},</if>
            <if test="sourceDocCode != null">#{sourceDocCode},</if>
            <if test="sourceDocType != null">#{sourceDocType},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="sourceLineId != null">#{sourceLineId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="quantityIssued != null">#{quantityIssued},</if>
            <if test="quantityAvailable != null">#{quantityAvailable},</if>
            <if test="quantityUsed != null">#{quantityUsed},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProTaskIssue" parameterType="ProTaskIssue">
        update pro_task_issue
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="sourceDocId != null">source_doc_id = #{sourceDocId},</if>
            <if test="sourceDocCode != null">source_doc_code = #{sourceDocCode},</if>
            <if test="sourceDocType != null">source_doc_type = #{sourceDocType},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="sourceLineId != null">source_line_id = #{sourceLineId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="quantityIssued != null">quantity_issued = #{quantityIssued},</if>
            <if test="quantityAvailable != null">quantity_available = #{quantityAvailable},</if>
            <if test="quantityUsed != null">quantity_used = #{quantityUsed},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteProTaskIssueByRecordId" parameterType="Long">
        delete from pro_task_issue where record_id = #{recordId}
    </delete>

    <delete id="deleteProTaskIssueByRecordIds" parameterType="String">
        delete from pro_task_issue where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>