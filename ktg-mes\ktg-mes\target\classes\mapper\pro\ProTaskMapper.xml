<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskMapper">

    <resultMap type="ProTask" id="ProTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="routeId" column="route_id" ></result>
        <result property="routeCode" column="route_code"></result>
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="isCheck"    column="is_check"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName"    column="unit_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="quantityProduced"    column="quantity_produced"    />
        <result property="quantityChanged"    column="quantity_changed"    />
        <result property="status"    column="status"    />
        <result property="quantityQuanlify"    column="quantity_quanlify"    />
        <result property="quantityUnquanlify"    column="quantity_unquanlify"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientNick"    column="client_nick"    />
        <result property="startTime"    column="start_time"    />
        <result property="duration"    column="duration"    />
        <result property="endTime"    column="end_time"    />
        <result property="colorCode"    column="color_code"    />
        <result property="requestDate"    column="request_date"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="cancelDate"    column="cancel_date"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />

    </resultMap>


    <sql id="selectProTaskVo">
        select t.task_id, t.task_code, t.task_name, t.workorder_id, t.workorder_code, t.workorder_name,
               t.workstation_id, t.workstation_code, t.workstation_name,
               t.route_id, t.route_code,
               t.process_id, t.process_code, t.process_name,p.is_check,
               t.item_id, t.item_code, t.item_name, t.specification, t.unit_of_measure, t.unit_name,
               t.quantity, t.quantity_produced, t.quantity_changed,
               t.client_id, t.client_code, t.client_name, t.client_nick,
               t.start_time, t.duration, t.end_time, t.color_code, t.request_date, t.finish_date, t.cancel_date,
               t.remark, t.attr1, t.attr2, t.attr3, t.attr4, t.create_by, t.create_time, t.update_by, t.update_time,
               t.status, t.quantity_quanlify, t.quantity_unquanlify
        from pro_task t
             left join pro_route_process p
             on t.route_id = p.route_id and t.process_id = p.process_id
    </sql>

    <select id="selectProTaskList" parameterType="ProTask" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        <where>
            <if test="taskCode != null  and taskCode != ''"> and t.task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="workorderId != null "> and t.workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and t.workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and t.workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="workstationId != null "> and t.workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and t.workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and t.workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="routeId != null "> and t.route_id = #{routeId}</if>
            <if test="routeCode != null and routeCode !=''"> and t.route_code = #{routeCode}</if>
            <if test="processId != null "> and t.process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and t.process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and t.process_name like concat('%', #{processName}, '%')</if>
            <if test="itemId != null "> and t.item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and t.item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and t.item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and t.specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and t.unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and t.unit_name like concat('%', #{unitName}, '%')</if>
            <if test="quantity != null "> and t.quantity = #{quantity}</if>
            <if test="quantityProduced != null "> and t.quantity_produced = #{quantityProduced}</if>
            <if test="quantityChanged != null "> and t.quantity_changed = #{quantityChanged}</if>
            <if test="clientId != null "> and t.client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and t.client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and t.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientNick != null  and clientNick != ''"> and t.client_nick = #{clientNick}</if>
            <if test="startTime != null "> and t.start_time = #{startTime}</if>
            <if test="duration != null "> and t.duration = #{duration}</if>
            <if test="endTime != null "> and t.end_time = #{endTime}</if>
            <if test="colorCode != null  and colorCode != ''"> and t.color_code = #{colorCode}</if>
            <if test="requestDate != null "> and t.request_date = #{requestDate}</if>
            <if test="finishDate != null "> and t.finish_date = #{finishDate}</if>
            <if test="cancelDate != null "> and t.cancel_date = #{cancelDate}</if>
            <if test="attr1 != null  and attr1 != ''"> and t.attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and t.attr2 = #{attr2}</if>
            <if test="attr3 != null "> and t.attr3 = #{attr3}</if>
            <if test="attr4 != null "> and t.attr4 = #{attr4}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="quantityQuanlify != null "> and t.quantity_quanlify = #{quantityQuanlify}</if>
            <if test="quantityUnquanlify != null "> and t.quantity_unquanlify = #{quantityUnquanlify}</if>
        </where>
    </select>
    
    <select id="selectProTaskByTaskId" parameterType="Long" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        where task_id = #{taskId}
    </select>

    <select id="selectProTaskProcessViewByWorkorder" parameterType="Long" resultMap="ProTaskResult">
        select a.process_id,a.process_code,a.process_name,IFNULL(b.quantity,0) as quantity,IFNULL(b.quantity_produced,0) as quantity_produced,IFNULL(b.quantity_quanlify,0) as quantity_quanlify,IFNULL(b.quantity_unquanlify,0) as quantity_unquanlify
        from (
                 select t.*
                 from pro_workorder pw
                          left join (
                     SELECT
                         prp.item_id,
                         psp.process_id,
                         psp.process_code,
                         psp.process_name,
                         psp.order_num
                     FROM
                         pro_route_product prp
                             LEFT JOIN pro_route_process psp
                                       ON prp.route_id = psp.route_id
                 ) t
                                    on pw.product_id = t.item_id
                 where pw.workorder_id = #{workorderId}
             )a
                 left join
             (
                 SELECT
                     pt.item_id,
                     pt.process_id,
                     pt.process_code,
                     pt.process_name,
                     sum( pt.quantity ) AS quantity,
                     sum( pt.quantity_produced ) AS quantity_produced,
                     sum( pt.quantity_quanlify ) AS quantity_quanlify,
                     sum( pt.quantity_unquanlify ) AS quantity_unquanlify
                 FROM
                     pro_task pt
                 WHERE
                     pt.workorder_id = #{workorderId}
                 GROUP BY
                     pt.item_id,
                     pt.process_id,
                     pt.process_code,
                     pt.process_name
             )b
             on a.item_id = b.item_id and a.process_id = b.process_id
    </select>

    <insert id="insertProTask" parameterType="ProTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into pro_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="workorderName != null and workorderName != ''">workorder_name,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null and workstationCode != ''">workstation_code,</if>
            <if test="workstationName != null and workstationName != ''">workstation_name,</if>
            <if test="routeId != null">route_id,</if>
            <if test="routeCode != null">route_code,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="quantityProduced != null">quantity_produced,</if>
            <if test="quantityQuanlify != null ">quantity_quanlify,</if>
            <if test="quantityUnquanlify != null ">quantity_unquanlify,</if>
            <if test="quantityChanged != null">quantity_changed,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null and clientCode != ''">client_code,</if>
            <if test="clientName != null and clientName != ''">client_name,</if>
            <if test="clientNick != null">client_nick,</if>
            <if test="startTime != null">start_time,</if>
            <if test="duration != null">duration,</if>
            <if test="endTime != null">end_time,</if>
            <if test="colorCode != null">color_code,</if>
            <if test="requestDate != null">request_date,</if>
            <if test="finishDate != null">finish_date,</if>
            <if test="cancelDate != null">cancel_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">#{workorderName},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null and workstationCode != ''">#{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">#{workstationName},</if>
            <if test="routeId != null">#{routeId},</if>
            <if test="routeCode != null">#{routeCode},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="quantityProduced != null">#{quantityProduced},</if>
            <if test="quantityQuanlify != null ">#{quantityQuanlify},</if>
            <if test="quantityUnquanlify != null ">#{quantityUnquanlify},</if>
            <if test="quantityChanged != null">#{quantityChanged},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null and clientCode != ''">#{clientCode},</if>
            <if test="clientName != null and clientName != ''">#{clientName},</if>
            <if test="clientNick != null">#{clientNick},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="colorCode != null">#{colorCode},</if>
            <if test="requestDate != null">#{requestDate},</if>
            <if test="finishDate != null">#{finishDate},</if>
            <if test="cancelDate != null">#{cancelDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProTask" parameterType="ProTask">
        update pro_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">workorder_name = #{workorderName},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null and workstationCode != ''">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">workstation_name = #{workstationName},</if>
            <if test="routeId != null">route_id = #{routeId},</if>
            <if test="routeCode != null and routeCode !=''">route_code = #{routeCode},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="quantityProduced != null">quantity_produced = #{quantityProduced},</if>
            <if test="quantityQuanlify != null ">quantity_quanlify = #{quantityQuanlify},</if>
            <if test="quantityUnquanlify != null ">quantity_unquanlify = #{quantityUnquanlify},</if>
            <if test="quantityChanged != null">quantity_changed = #{quantityChanged},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null and clientCode != ''">client_code = #{clientCode},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="clientNick != null">client_nick = #{clientNick},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="colorCode != null">color_code = #{colorCode},</if>
            <if test="requestDate != null">request_date = #{requestDate},</if>
            <if test="finishDate != null">finish_date = #{finishDate},</if>
            <if test="cancelDate != null">cancel_date = #{cancelDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteProTaskByTaskId" parameterType="Long">
        delete from pro_task where task_id = #{taskId}
    </delete>

    <delete id="deleteProTaskByTaskIds" parameterType="String">
        delete from pro_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>