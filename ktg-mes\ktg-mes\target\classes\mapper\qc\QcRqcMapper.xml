<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcRqcMapper">
    
    <resultMap type="QcRqc" id="QcRqcResult">
        <result property="rqcId"    column="rqc_id"    />
        <result property="rqcCode"    column="rqc_code"    />
        <result property="rqcName"    column="rqc_name"    />
        <result property="templateId"    column="template_id"    />
        <result property="sourceDocId"    column="source_doc_id"    />
        <result property="sourceDocType"    column="source_doc_type"    />
        <result property="sourceDocCode"    column="source_doc_code"    />
        <result property="sourceLineId"    column="source_line_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName"    column="unit_name"    />
        <result property="batchId"    column="batch_id"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="quantityCheck"    column="quantity_check"    />
        <result property="quantityUnqualified"    column="quantity_unqualified"    />
        <result property="quantityQualified"    column="quantity_qualified"    />
        <result property="checkResult"    column="check_result"    />
        <result property="inspectDate"    column="inspect_date"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcRqcVo">
        select rqc_id, rqc_code, rqc_name, template_id, source_doc_id, source_doc_type, source_doc_code, source_line_id,item_id, item_code, item_name, specification, unit_of_measure, unit_name, batch_id, batch_code, quantity_check, quantity_unqualified, quantity_qualified, check_result, inspect_date, user_id, user_name, nick_name, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from qc_rqc
    </sql>

    <select id="selectQcRqcList" parameterType="QcRqc" resultMap="QcRqcResult">
        <include refid="selectQcRqcVo"/>
        <where>  
            <if test="rqcCode != null  and rqcCode != ''"> and rqc_code = #{rqcCode}</if>
            <if test="rqcName != null  and rqcName != ''"> and rqc_name like concat('%', #{rqcName}, '%')</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="sourceDocId != null "> and source_doc_id = #{sourceDocId}</if>
            <if test="sourceDocType != null  and sourceDocType != ''"> and source_doc_type = #{sourceDocType}</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and source_doc_code = #{sourceDocCode}</if>
            <if test="sourceLineId != null "> and source_line_id = #{sourceLineId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="batchId != null "> and batch_id = #{batchId}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="quantityCheck != null "> and quantity_check = #{quantityCheck}</if>
            <if test="quantityUnqualified != null "> and quantity_unqualified = #{quantityUnqualified}</if>
            <if test="quantityQualified != null "> and quantity_qualified = #{quantityQualified}</if>
            <if test="checkResult != null  and checkResult != ''"> and check_result = #{checkResult}</if>
            <if test="inspectDate != null "> and inspect_date = #{inspectDate}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectQcRqcByRqcId" parameterType="Long" resultMap="QcRqcResult">
        <include refid="selectQcRqcVo"/>
        where rqc_id = #{rqcId}
    </select>

    <select id="checkCodeUnique" parameterType="QcIqc" resultMap="QcRqcResult">
        <include refid="selectQcRqcVo"/>
        where rqc_code = #{rqcCode} limit 1
    </select>

    <insert id="insertQcRqc" parameterType="QcRqc" useGeneratedKeys="true" keyProperty="rqcId">
        insert into qc_rqc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rqcCode != null and rqcCode != ''">rqc_code,</if>
            <if test="rqcName != null">rqc_name,</if>
            <if test="templateId != null">template_id,</if>
            <if test="sourceDocId != null">source_doc_id,</if>
            <if test="sourceDocType != null">source_doc_type,</if>
            <if test="sourceDocCode != null">source_doc_code,</if>
            <if test="sourceLineId != null">source_line_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="quantityCheck != null">quantity_check,</if>
            <if test="quantityUnqualified != null">quantity_unqualified,</if>
            <if test="quantityQualified != null">quantity_qualified,</if>
            <if test="checkResult != null">check_result,</if>
            <if test="inspectDate != null">inspect_date,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rqcCode != null and rqcCode != ''">#{rqcCode},</if>
            <if test="rqcName != null">#{rqcName},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="sourceDocId != null">#{sourceDocId},</if>
            <if test="sourceDocType != null">#{sourceDocType},</if>
            <if test="sourceDocCode != null">#{sourceDocCode},</if>
            <if test="sourceLineId != null">#{sourceLineId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="quantityCheck != null">#{quantityCheck},</if>
            <if test="quantityUnqualified != null">#{quantityUnqualified},</if>
            <if test="quantityQualified != null">#{quantityQualified},</if>
            <if test="checkResult != null">#{checkResult},</if>
            <if test="inspectDate != null">#{inspectDate},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQcRqc" parameterType="QcRqc">
        update qc_rqc
        <trim prefix="SET" suffixOverrides=",">
            <if test="rqcCode != null and rqcCode != ''">rqc_code = #{rqcCode},</if>
            <if test="rqcName != null">rqc_name = #{rqcName},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="sourceDocId != null">source_doc_id = #{sourceDocId},</if>
            <if test="sourceDocType != null">source_doc_type = #{sourceDocType},</if>
            <if test="sourceDocCode != null">source_doc_code = #{sourceDocCode},</if>
            <if test="sourceLineId != null">source_line_id = #{sourceLineId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="quantityCheck != null">quantity_check = #{quantityCheck},</if>
            <if test="quantityUnqualified != null">quantity_unqualified = #{quantityUnqualified},</if>
            <if test="quantityQualified != null">quantity_qualified = #{quantityQualified},</if>
            <if test="checkResult != null">check_result = #{checkResult},</if>
            <if test="inspectDate != null">inspect_date = #{inspectDate},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rqc_id = #{rqcId}
    </update>

    <delete id="deleteQcRqcByRqcId" parameterType="Long">
        delete from qc_rqc where rqc_id = #{rqcId}
    </delete>

    <delete id="deleteQcRqcByRqcIds" parameterType="String">
        delete from qc_rqc where rqc_id in 
        <foreach item="rqcId" collection="array" open="(" separator="," close=")">
            #{rqcId}
        </foreach>
    </delete>
</mapper>