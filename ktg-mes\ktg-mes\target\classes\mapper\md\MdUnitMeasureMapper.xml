<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdUnitMeasureMapper">

    <resultMap type="MdUnitMeasure" id="MdUnitMeasureResult">
        <result property="measureId"    column="measure_id"    />
        <result property="measureCode"    column="measure_code"    />
        <result property="measureName"    column="measure_name"    />
        <result property="primaryFlag"    column="primary_flag"    />
        <result property="primaryId"    column="primary_id"    />
        <result property="changeRate"    column="change_rate"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdUnitMeasureVo">
        select measure_id, measure_code, measure_name, primary_flag, primary_id, change_rate, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_unit_measure
    </sql>

    <select id="selectMdUnitMeasureList" parameterType="MdUnitMeasure" resultMap="MdUnitMeasureResult">
        <include refid="selectMdUnitMeasureVo"/>
        <where>
            <if test="measureCode != null  and measureCode != ''"> and measure_code = #{measureCode}</if>
            <if test="measureName != null  and measureName != ''"> and measure_name like concat('%', #{measureName}, '%')</if>
            <if test="primaryFlag != null  and primaryFlag != ''"> and primary_flag = #{primaryFlag}</if>
            <if test="primaryId != null "> and primary_id = #{primaryId}</if>
            <if test="changeRate != null "> and change_rate = #{changeRate}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>

    <select id="checkMeasureUnitCodeUnique" parameterType="MdUnitMeasure" resultMap="MdUnitMeasureResult">
        <include refid="selectMdUnitMeasureVo"/>
        where measure_code = #{measureCode} limit 1
    </select>

    <select id="selectMdUnitMeasureByMeasureId" parameterType="Long" resultMap="MdUnitMeasureResult">
        <include refid="selectMdUnitMeasureVo"/>
        where measure_id = #{measureId}
    </select>

    <select id="selectMdUnitByCode" parameterType="String" resultMap="MdUnitMeasureResult">
        <include refid="selectMdUnitMeasureVo"/>
        where measure_code = #{unitCode}
    </select>

    <insert id="insertMdUnitMeasure" parameterType="MdUnitMeasure" useGeneratedKeys="true" keyProperty="measureId">
        insert into md_unit_measure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="measureCode != null and measureCode != ''">measure_code,</if>
            <if test="measureName != null and measureName != ''">measure_name,</if>
            <if test="primaryFlag != null and primaryFlag != ''">primary_flag,</if>
            <if test="primaryId != null">primary_id,</if>
            <if test="changeRate != null">change_rate,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="measureCode != null and measureCode != ''">#{measureCode},</if>
            <if test="measureName != null and measureName != ''">#{measureName},</if>
            <if test="primaryFlag != null and primaryFlag != ''">#{primaryFlag},</if>
            <if test="primaryId != null">#{primaryId},</if>
            <if test="changeRate != null">#{changeRate},</if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdUnitMeasure" parameterType="MdUnitMeasure">
        update md_unit_measure
        <trim prefix="SET" suffixOverrides=",">
            <if test="measureCode != null and measureCode != ''">measure_code = #{measureCode},</if>
            <if test="measureName != null and measureName != ''">measure_name = #{measureName},</if>
            <if test="primaryFlag != null and primaryFlag != ''">primary_flag = #{primaryFlag},</if>
            <if test="primaryId != null">primary_id = #{primaryId},</if>
            <if test="changeRate != null">change_rate = #{changeRate},</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where measure_id = #{measureId}
    </update>

    <delete id="deleteMdUnitMeasureByMeasureId" parameterType="Long">
        delete from md_unit_measure where measure_id = #{measureId}
    </delete>

    <delete id="deleteMdUnitMeasureByMeasureIds" parameterType="String">
        delete from md_unit_measure where measure_id in
        <foreach item="measureId" collection="array" open="(" separator="," close=")">
            #{measureId}
        </foreach>
    </delete>
</mapper>
