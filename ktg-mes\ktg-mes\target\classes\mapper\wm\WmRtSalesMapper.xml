<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmRtSalesMapper">
    
    <resultMap type="WmRtSales" id="WmRtSalesResult">
        <result property="rtId"    column="rt_id"    />
        <result property="rtCode"    column="rt_code"    />
        <result property="rtName"    column="rt_name"    />
        <result property="soCode"    column="so_code"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientNick"    column="client_nick"    />
        <result property="rtDate"    column="rt_date"    />
        <result property="rtReason"    column="rt_reason"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="RtSalesTxBean" id="RtSalesTxBeanResult">
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="unitName" column="unit_name"></result>
        <result property="batchId" column="batch_id"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="rtDate" column="rt_date"></result>
    </resultMap>

    <sql id="selectWmRtSalesVo">
        select rt_id, rt_code, rt_name, so_code, client_id, client_code, client_name, client_nick, rt_date, rt_reason, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_rt_sales
    </sql>

    <select id="getTxBeans" parameterType="Long" resultMap="RtSalesTxBeanResult">
        SELECT irl.`item_id`,
               irl.`item_code`,
               irl.`item_name`,
               irl.`specification`,
               irl.`unit_of_measure`,
               irl.`unit_name`,
               ird.`batch_id`,
               ird.`batch_code`,
               ird.`warehouse_id`,ird.`warehouse_code`,ird.`warehouse_name`,
               ird.`location_id`,ird.`location_code`,ird.`location_name`,
               ird.`area_id`,ird.`area_code`,ird.`area_name`,
               'RT_SALES' AS source_doc_type,ir.`rt_id` AS source_doc_id,
               ir.`rt_code` AS source_doc_code,
               irl.`line_id` AS source_doc_line_id,
               irl.quantity_rted AS transaction_quantity,
               ir.rt_date as recptDate
        FROM wm_rt_sales ir
                 LEFT JOIN wm_rt_sales_line irl
                           ON ir.rt_id = irl.rt_id
                 LEFT JOIN wm_rt_sales_detail ird
                           ON irl.line_id = ird.line_id and ir.rt_id = ird.rt_id
        WHERE ir.rt_id = #{rtId}
    </select>

    <select id="selectWmRtSalesList" parameterType="WmRtSales" resultMap="WmRtSalesResult">
        <include refid="selectWmRtSalesVo"/>
        <where>  
            <if test="rtCode != null  and rtCode != ''"> and rt_code = #{rtCode}</if>
            <if test="rtName != null  and rtName != ''"> and rt_name like concat('%', #{rtName}, '%')</if>
            <if test="soCode != null  and soCode != ''"> and so_code = #{soCode}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientNick != null  and clientNick != ''"> and client_nick = #{clientNick}</if>
            <if test="rtDate != null "> and rt_date = #{rtDate}</if>
            <if test="rtReason != null  and rtReason != ''"> and rt_reason = #{rtReason}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmRtSalesByRtId" parameterType="Long" resultMap="WmRtSalesResult">
        <include refid="selectWmRtSalesVo"/>
        where rt_id = #{rtId}
    </select>

    <select id="checkUnique" parameterType="WmRtSales" resultMap="WmRtSalesResult">
        <include refid="selectWmRtSalesVo"/>
        where rt_code = #{rtCode}
    </select>


    <insert id="insertWmRtSales" parameterType="WmRtSales" useGeneratedKeys="true" keyProperty="rtId">
        insert into wm_rt_sales
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">rt_code,</if>
            <if test="rtName != null and rtName != ''">rt_name,</if>
            <if test="soCode != null">so_code,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientNick != null">client_nick,</if>
            <if test="rtDate != null">rt_date,</if>
            <if test="rtReason != null">rt_reason,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">#{rtCode},</if>
            <if test="rtName != null and rtName != ''">#{rtName},</if>
            <if test="soCode != null">#{soCode},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientNick != null">#{clientNick},</if>
            <if test="rtDate != null">#{rtDate},</if>
            <if test="rtReason != null">#{rtReason},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmRtSales" parameterType="WmRtSales">
        update wm_rt_sales
        <trim prefix="SET" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">rt_code = #{rtCode},</if>
            <if test="rtName != null and rtName != ''">rt_name = #{rtName},</if>
            <if test="soCode != null">so_code = #{soCode},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientNick != null">client_nick = #{clientNick},</if>
            <if test="rtDate != null">rt_date = #{rtDate},</if>
            <if test="rtReason != null">rt_reason = #{rtReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rt_id = #{rtId}
    </update>

    <delete id="deleteWmRtSalesByRtId" parameterType="Long">
        delete from wm_rt_sales where rt_id = #{rtId}
    </delete>

    <delete id="deleteWmRtSalesByRtIds" parameterType="String">
        delete from wm_rt_sales where rt_id in 
        <foreach item="rtId" collection="array" open="(" separator="," close=")">
            #{rtId}
        </foreach>
    </delete>
</mapper>