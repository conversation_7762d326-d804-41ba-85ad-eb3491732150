<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmStockTakingLineMapper">
    
    <resultMap type="WmStockTakingLine" id="WmStockTakingLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="takingId"    column="taking_id"    />
        <result property="materialStockId"    column="material_stock_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName"    column="unit_name"    />
        <result property="batchId" column="batch_id"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="quantity"    column="quantity"    />
        <result property="takingQuantity"    column="taking_quantity"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="takingStatus"    column="taking_status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmStockTakingLineVo">
        select line_id, taking_id, material_stock_id, item_id, item_code, item_name, specification, unit_of_measure, unit_name,batch_id, batch_code, quantity, taking_quantity, warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, taking_status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_stock_taking_line
    </sql>

    <select id="selectWmStockTakingLineList" parameterType="WmStockTakingLine" resultMap="WmStockTakingLineResult">
        <include refid="selectWmStockTakingLineVo"/>
        <where>  
            <if test="takingId != null "> and taking_id = #{takingId}</if>
            <if test="materialStockId != null "> and material_stock_id = #{materialStockId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="batchId != null "> and batch_id = #{batchId}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="takingQuantity != null "> and taking_quantity = #{takingQuantity}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="takingStatus != null  and takingStatus != ''"> and taking_status = #{takingStatus}</if>
        </where>
        order by area_code,item_id,batch_code desc
    </select>
    
    <select id="selectWmStockTakingLineByLineId" parameterType="Long" resultMap="WmStockTakingLineResult">
        <include refid="selectWmStockTakingLineVo"/>
        where line_id = #{lineId}
    </select>
        
    <insert id="insertWmStockTakingLine" parameterType="WmStockTakingLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into wm_stock_taking_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="takingId != null">taking_id,</if>
            <if test="materialStockId != null">material_stock_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="quantity != null">quantity,</if>
            <if test="takingQuantity != null">taking_quantity,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="takingStatus != null and takingStatus != ''">taking_status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="takingId != null">#{takingId},</if>
            <if test="materialStockId != null">#{materialStockId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="takingQuantity != null">#{takingQuantity},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="takingStatus != null and takingStatus != ''">#{takingStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmStockTakingLine" parameterType="WmStockTakingLine">
        update wm_stock_taking_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="takingId != null">taking_id = #{takingId},</if>
            <if test="materialStockId != null">material_stock_id = #{materialStockId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="takingQuantity != null">taking_quantity = #{takingQuantity},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="takingStatus != null and takingStatus != ''">taking_status = #{takingStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <update id="updateTakingStatus" parameterType="WmStockTakingLine">
        update wm_stock_taking_line a
        join (
            select taking_id, line_id, quantity, taking_quantity,
            case when IFNULL(taking_quantity,0) &gt; IFNULL(quantity,0) then 'PROFIT'
            when   IFNULL(taking_quantity,0) &lt; IFNULL(quantity,0) then 'LOSS'
            else 'NORMAL' end as taking_status
            from (
                select sl.taking_id,sl.line_id,sl.specification, sl.quantity,sum(IFNULL(sr.quantity,0)) as taking_quantity
                from wm_stock_taking_line sl
                left join wm_stock_taking_result sr
                on sl.line_id = sr.line_id
                where sl.taking_id =  #{takingId}
                and sl.line_id = #{lineId}
            ) t
        ) b on a.taking_id = b.taking_id and a.line_id = b.line_id
            set a.taking_quantity = b.taking_quantity,
                a.taking_status = b.taking_status
        where a.taking_id =  #{takingId}
        and a.line_id = #{lineId}
    </update>

    <update id="updateFrozenStatus">
        update wm_material_stock
        set frozen_flag = #{frozenFlag}
        where material_stock_id in (
            select material_stock_id
            from wm_stock_taking_line
            where taking_id = #{takingId}
              and material_stock_id is not null
        )
    </update>

    <delete id="deleteWmStockTakingLineByLineId" parameterType="Long">
        delete from wm_stock_taking_line where line_id = #{lineId}
    </delete>

    <delete id="deleteWmStockTakingLineByLineIds" parameterType="String">
        delete from wm_stock_taking_line where line_id in 
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteByTakingId" parameterType="Long">
        delete from wm_stock_taking_line where taking_id = #{takingId}
    </delete>

</mapper>