<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmTransactionMapper">
    
    <resultMap type="WmTransaction" id="WmTransactionResult">
        <result property="transactionId"    column="transaction_id"    />
        <result property="transactionType"    column="transaction_type"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName"    column="unit_name"    />
        <result property="batchId"    column="batch_id"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="sourceDocType"    column="source_doc_type"    />
        <result property="sourceDocId"    column="source_doc_id"    />
        <result property="sourceDocCode"    column="source_doc_code"    />
        <result property="sourceDocLineId"    column="source_doc_line_id"    />
        <result property="materialStockId"    column="material_stock_id"    />
        <result property="transactionFlag"    column="transaction_flag"    />
        <result property="transactionQuantity"    column="transaction_quantity"    />
        <result property="transactionDate"    column="transaction_date"    />
        <result property="relatedTransactionId"    column="related_transaction_id"    />
        <result property="recptDate" column="recpt_date"></result>
        <result property="erpDate"    column="erp_date"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmTransactionVo">
        select transaction_id, transaction_type, item_id, item_code, item_name, specification, unit_of_measure, unit_name, batch_id, batch_code, warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, source_doc_type, source_doc_id, source_doc_code, source_doc_line_id, material_stock_id, transaction_flag, transaction_quantity, transaction_date, related_transaction_id, erp_date,recpt_date, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_transaction
    </sql>

    <select id="selectWmTransactionList" parameterType="WmTransaction" resultMap="WmTransactionResult">
        <include refid="selectWmTransactionVo"/>
        <where>  
            <if test="transactionType != null  and transactionType != ''"> and transaction_type = #{transactionType}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="batchId != null "> and batch_id = #{batchId}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>

            <if test="sourceDocType != null  and sourceDocType != ''"> and source_doc_type = #{sourceDocType}</if>
            <if test="sourceDocId != null "> and source_doc_id = #{sourceDocId}</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and source_doc_code = #{sourceDocCode}</if>
            <if test="sourceDocLineId != null "> and source_doc_line_id = #{sourceDocLineId}</if>
            <if test="materialStockId != null "> and material_stock_id = #{materialStockId}</if>
            <if test="transactionFlag != null "> and transaction_flag = #{transactionFlag}</if>
            <if test="transactionQuantity != null "> and transaction_quantity = #{transactionQuantity}</if>
            <if test="transactionDate != null "> and transaction_date = #{transactionDate}</if>
            <if test="relatedTransactionId != null "> and related_transaction_id = #{relatedTransactionId}</if>
            <if test="recptDate ！= null"> and recpt_date = #{recptDate}</if>

            <if test="expireDate != null "> and expire_date = #{expireDate}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>
    
    <select id="selectWmTransactionByTransactionId" parameterType="Long" resultMap="WmTransactionResult">
        <include refid="selectWmTransactionVo"/>
        where transaction_id = #{transactionId}
    </select>
        
    <insert id="insertWmTransaction" parameterType="WmTransaction" useGeneratedKeys="true" keyProperty="transactionId">
        insert into wm_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="transactionType != null and transactionType != ''">transaction_type,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>

            <if test="sourceDocType != null">source_doc_type,</if>
            <if test="sourceDocId != null">source_doc_id,</if>
            <if test="sourceDocCode != null">source_doc_code,</if>
            <if test="sourceDocLineId != null">source_doc_line_id,</if>
            <if test="materialStockId != null">material_stock_id,</if>
            <if test="transactionFlag != null">transaction_flag,</if>
            <if test="transactionQuantity != null">transaction_quantity,</if>
            <if test="transactionDate != null">transaction_date,</if>
            <if test="relatedTransactionId != null">related_transaction_id,</if>
            <if test="erpDate != null">erp_date,</if>
            <if test="recptDate != null">recpt_date,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="transactionType != null and transactionType != ''">#{transactionType},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>

            <if test="sourceDocType != null">#{sourceDocType},</if>
            <if test="sourceDocId != null">#{sourceDocId},</if>
            <if test="sourceDocCode != null">#{sourceDocCode},</if>
            <if test="sourceDocLineId != null">#{sourceDocLineId},</if>
            <if test="materialStockId != null">#{materialStockId},</if>
            <if test="transactionFlag != null">#{transactionFlag},</if>
            <if test="transactionQuantity != null">#{transactionQuantity},</if>
            <if test="transactionDate != null">#{transactionDate},</if>
            <if test="relatedTransactionId != null">#{relatedTransactionId},</if>
            <if test="erpDate != null">#{erpDate},</if>
            <if test="recptDate != null">#{recptDate},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmTransaction" parameterType="WmTransaction">
        update wm_transaction
        <trim prefix="SET" suffixOverrides=",">
            <if test="transactionType != null and transactionType != ''">transaction_type = #{transactionType},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>

            <if test="sourceDocType != null">source_doc_type = #{sourceDocType},</if>
            <if test="sourceDocId != null">source_doc_id = #{sourceDocId},</if>
            <if test="sourceDocCode != null">source_doc_code = #{sourceDocCode},</if>
            <if test="sourceDocLineId != null">source_doc_line_id = #{sourceDocLineId},</if>
            <if test="materialStockId != null">material_stock_id = #{materialStockId},</if>
            <if test="transactionFlag != null">transaction_flag = #{transactionFlag},</if>
            <if test="transactionQuantity != null">transaction_quantity = #{transactionQuantity},</if>
            <if test="transactionDate != null">transaction_date = #{transactionDate},</if>
            <if test="relatedTransactionId != null">related_transaction_id = #{relatedTransactionId},</if>
            <if test="erpDate != null">erp_date = #{erpDate},</if>
            <if test="recptDate != null">recp_date = #{recptDate},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where transaction_id = #{transactionId}
    </update>

    <delete id="deleteWmTransactionByTransactionId" parameterType="Long">
        delete from wm_transaction where transaction_id = #{transactionId}
    </delete>

    <delete id="deleteWmTransactionByTransactionIds" parameterType="String">
        delete from wm_transaction where transaction_id in 
        <foreach item="transactionId" collection="array" open="(" separator="," close=")">
            #{transactionId}
        </foreach>
    </delete>
</mapper>