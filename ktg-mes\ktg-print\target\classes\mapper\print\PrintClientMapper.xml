<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.print.mapper.PrintClientMapper">

    <resultMap type="PrintClient" id="PrintClientResult">
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientIp"    column="client_ip"    />
        <result property="clientPort"    column="client_port"    />
        <result property="clientToken"    column="client_token"    />
        <result property="status"    column="status"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopCode"    column="workshop_code"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPrintClientVo">
        select client_id, client_code, client_name, client_ip, client_port, client_token, workshop_id, workshop_code, workshop_name, workstation_id, workstation_code, workstation_name, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from print_client
    </sql>

    <select id="getClientList" resultType="com.ktg.print.domain.PrintClient" resultMap="PrintClientResult">
        <include refid="selectPrintClientVo"/>
        <where>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientIp != null  and clientIp != ''"> and client_ip = #{clientIp}</if>
            <if test="clientPort != null"> and client_port = #{clientPort}</if>
            <if test="clientToken != null  and clientToken != ''"> and client_token = #{clientToken}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="workshopCode != null  and workshopCode != ''"> and workshop_code = #{workshopCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstationCode = #{workstationCode}</if>
        </where>
    </select>

    <select id="checkCilentCodeUnique" resultType="com.ktg.print.domain.PrintClient" resultMap="PrintClientResult">
        <include refid="selectPrintClientVo"/>
        where client_code = #{clientCode}
        limit 1
    </select>
    <select id="selectById" resultType="com.ktg.print.domain.PrintClient" resultMap="PrintClientResult">
        <include refid="selectPrintClientVo"/>
        where client_id = #{clientId}
        limit 1
    </select>

    <insert id="insertClient" parameterType="PrintClient" useGeneratedKeys="true" keyProperty="clientId">
        insert into print_client
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientIp != null and clientIp != ''">client_ip,</if>
            <if test="clientPort != null">client_port,</if>
            <if test="clientToken != null">client_token,</if>
            <if test="status != null">status,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="workshopCode != null">workshop_code,</if>
            <if test="workshopName != null">workshop_name,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientIp != null and clientIp != ''">#{clientIp},</if>
            <if test="clientPort != null">#{clientPort},</if>
            <if test="clientToken != null">#{clientToken},</if>
            <if test="status != null">#{status},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="workshopCode != null">#{workshopCode},</if>
            <if test="workshopName != null">#{workshopName},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateClient" parameterType="PrintClient">
        update print_client
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientIp != null and clientIp != ''">client_ip = #{clientIp},</if>
            <if test="clientPort != null">client_port = #{clientPort},</if>
            <if test="clientToken != null">client_token = #{clientToken},</if>
            <if test="status != null">status = #{status},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="workshopCode != null">workshop_code = #{workshopCode},</if>
            <if test="workshopName != null">workshop_name = #{workshopName},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where client_id = #{clientId}
    </update>

    <delete id="deleteByIds">
        delete from print_client where client_id in
        <foreach item="item" collection="clientIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>